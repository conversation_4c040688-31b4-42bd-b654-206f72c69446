VERSION=$1

case $SSG_PROD_TYPE in
  ssg)
    QA_SOURCE_PREFIX=https://ssg-qa-intranet.seasungame.com/ssglauncher/ssg/web/assets
    ;;
  gp)
    QA_SOURCE_PREFIX=https://ssg-qa-g-intranet.seasungame.com/ssglauncher/gp/web/assets
    ;;
  *)
    echo "SSG_PROD_TYPE $SSG_PROD_TYPE is illegal"
    ;;
esac

PROJECT_NAME="${SSG_PROJECT}"
FILENAME="$PROJECT_NAME-$VERSION.zip"
QA_FILENAME="${PROJECT_NAME}-${VERSION_FROM_QA}.zip"
QA_SOURCE_URL="${QA_SOURCE_PREFIX}/${QA_FILENAME}"
echo "use qa version: $QA_FILENAME"
curl -O $QA_SOURCE_URL
if [ -f $QA_FILENAME ]; then
  mv $QA_FILENAME $FILENAME
else
  echo "qa $QA_FILENAME does not exist"
fi

# ./scripts/prod-publish-to-remote.sh "$PROJECT_NAME" "$VERSION" "$FILENAME"
python3 ./scripts/prod-publish-to-remote.py "$PROJECT_NAME" "$VERSION" "$FILENAME" "$SSG_PROD_TYPE"
