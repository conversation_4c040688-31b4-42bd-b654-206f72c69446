# ssglauncher station

Games: [![BK Pipelines Status](https://devops.seasungame.com/process/api/external/pipelines/projects/200001474/p-b45e989f666845618f0c5aa50a919add/badge?X-DEVOPS-PROJECT-ID=200001474)](https://devops.seasungame.com/process/api-html/user/builds/projects/200001474/pipelines/p-b45e989f666845618f0c5aa50a919add/latestFinished?X-DEVOPS-PROJECT-ID=200001474)

~~SSG 启动器网页部分（内嵌页）~~

~~包括不限于游戏页面、商店、社区（推栏）~~

**2023.9.11 调整：启动器正式开始使用 Web UI，所以该项目为启动器 UI Layer**

PS: `-station` 是 @tuilan 命名规则（PC 内嵌程序为 station，小程序为 miniapp）

## 项目介绍

## 开发指南

### ~~为什么使用 nx workspace~~

~~每个功能具有独立性又是相关性，之间存在功能复用但根本上可以说不是同一个项目~~

~~Nx 解决了同一个 `package.json` 中功能或者模块相对独立，但它们依然是同一个主体。且 Nx 解决了模块之间的依赖问题，可以清晰看到项目的整体架构与依赖关系~~

### 为什么不使用 nx workspace

nx 附带的大部分功能我们都用不上，但却被强制使用整个 workflow

这导致了一旦我们开始定制 workflow 我们需要编写很多 nx 插件

而 nx 插件并没有复用价值（也许只有启动器项目在使用）

### 启动项目

请保证全局安装了这些东西

- [pnpm](https://pnpm.io/)
- [rush](https://rushjs.io/zh-cn/pages/intro/get_started/)

启动步骤

1. clone
2. rush install
3. pnpm install
4. rush build -T ssglauncher-app-\* （\*：看你正在开发哪个子应用）
5. cd apps/ssglauncher-app-\*
6. rushx dev / pnpm dev

**开发环境启动步骤请参考** [development](./development/README.md)

### 目录解释与开发向导

### 提交信息

（这个项目不会使用 commitlint）

一般来说提交会常用几种：

- chore：一般的更改
- ci：跟 ci 相关
- docs：更新了文档文件（md 文件）
- feat：功能
- fix：修复
- perf：性能、体验
- refactor：重构
- revert：回退
- style：仅调整（代码）样式
- test：仅更新测试用例

这是一个 workspace，所以提交建议为 `feat(station): 新增主边栏`，更新 root 时省略说明

提交主信息尽可能简单且清晰，其他信息写到描述（commit 换行，第二行开始）

### 其他信息

## E2E 测试

~~所有 E2E 测试都使用 Cypress~~
