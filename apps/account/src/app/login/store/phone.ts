import { useCallback, useEffect } from 'preact/hooks';
import { atom, useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useAtomCallback } from 'jotai/utils';

// 手机号单位
export interface PhoneValue {
  /** 手机号文本（可能加密） */
  text: string;
  /** 手机号 OpenID */
  openId: string;
}

// 手机号所绑定的账号（可能是手机号）
interface TAccount {
  openId: string;
  text: string;
}

// 通行证数据单位
export interface TAccountPhoneData {
  /** 通行证 */
  account: TAccount;
  /** 手机列表 */
  phoneList: PhoneValue[];
}

// 通行证数据单位（被选择）
export interface TSelectedAccountPhoneData {
  /** 通行证 */
  account: TAccount;
  /** 手机 */
  phone: PhoneValue;
}

const AccountPhoneData = atom<TAccountPhoneData>({
  account: {
    openId: '',
    text: '',
  },
  phoneList: [],
});

const SelectedPhone = atom<PhoneValue>({
  text: '',
  openId: '',
});

const SelectedAccountPhoneData = atom<TSelectedAccountPhoneData>(get => {
  const account = get(AccountPhoneData).account;
  const phone = get(SelectedPhone);

  return {
    account,
    phone,
  };
});

const PhoneList = atom<PhoneValue[], [PhoneValue[]], unknown>(
  get => get(AccountPhoneData).phoneList,
  (get, set, value) => {
    const data = get(AccountPhoneData);

    if (!value.every(item => item.openId)) {
      console.error('缺少 openId');

      return;
    }

    set(AccountPhoneData, { ...data, phoneList: value });
  },
);

// 手机号通行证绑定了一个手机号，但不是手机号
const SingleAndNotEqualBySelf = atom<boolean>(get => get(PhoneList).length === 1);

const Account = atom<TAccount, [TAccount], unknown>(
  get => get(AccountPhoneData).account,
  (get, set, value) => {
    const data = get(AccountPhoneData);

    set(AccountPhoneData, { ...data, account: value });
  },
);

export function usePhoneStore() {
  const [list, setList] = useAtom(PhoneList);
  const [account, setAccount] = useAtom(Account);
  const setSelected = useSetAtom(SelectedPhone);
  const singleStatus = useAtomValue(SingleAndNotEqualBySelf);
  const selected = useAtomValue(SelectedAccountPhoneData);

  const syncSelectedData = useAtomCallback(
    useCallback((get, set) => {
      const phoneList = get(PhoneList);

      if (phoneList.length && phoneList.length === 1) {
        set(SelectedPhone, phoneList[0]);
        return;
      }
    }, []),
  );

  useEffect(() => {
    syncSelectedData();
  }, [syncSelectedData]);

  return {
    query: {
      account,
      list,
      singleStatus,
      selected,
    },
    commands: {
      setList,
      setAccount,
      setSelected,
    },
  };
}
