import { useCallback, useMemo } from 'preact/hooks';
import { atom, useAtom } from 'jotai';

const AutoLoginState = atom<boolean>(false);

const AutoLoginRemind = atom<boolean>(false);

export function useAutoLoginStore() {
  const [get, set] = useAtom(AutoLoginState);
  const [remind, setRemind] = useAtom(AutoLoginRemind);

  const setState = useCallback(
    (val: boolean) => {
      set(val);
    },
    [set],
  );

  const result = useMemo(
    () => ({
      query: {
        state: get,
        remind,
      },
      commands: {
        setState,
        setRemind,
      },
    }),
    [get, remind, setRemind, setState],
  );

  return result;
}
