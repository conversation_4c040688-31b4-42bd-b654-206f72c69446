import type { FormDto, PhoneLoginRequires } from '../views/PhoneLogin';
import type { ExtEnvSDK } from '#app/common/models/extEnvSDK';
import type { ServerError } from '#app/common/models/serverError';
import { VerifyCodeDTO } from '@ssglauncher/server-api';
import { useForm } from 'react-hook-form';
import { useCallback, useEffect, useState } from 'react';
import md5 from 'blueimp-md5';
import { CaptchaVerifyState, useCaptcha, useXCefUtil } from '@ssglauncher/hooks';
import { promisify } from '@ssglauncher/sdk';
import { AccountLoginTracking, AccountOperationTracking } from '@ssglauncher/tracking';
import { secretStr, isNetworkError } from '@ssglauncher/utils';
import { SMSApi, EChannel, EVerifyType } from '@ssglauncher/server-api';
import { createCenterToast } from '@ssglauncher/components';
import { useShowLastLoggedInTips } from '@ssglauncher/account/hooks';
import { useFlash } from '../../common/hooks/useFlash';
import { useAutoLoginChecker } from '../hooks/useAutoLoginChecker';
import { usePolicyChecker } from '../../common/hooks/usePolicyChecker';
import { useAutoLoginStore } from '../store/autoLogin';
import { useExceptionService } from './exception.service';
import { ELoginUIType } from '../store/accountLoginUI';
import { useSelectRecentAccount } from '../hooks/useSelectRecentAccount';
import { useRecentAccountStore } from '../store/recentAccount';
import { useRouter } from '#app/common/hooks/useRouter';
import { initRequest } from '#app/common/utils/request';
import { SMS_CODE_LOGIN_STORE_KEY } from '#app/login/store/smsCodeLoginStore';
import { useStore } from '#app/common/utils/store';
import { CURRENT_LOGIN_STORE_KEY } from '#app/login/store/currentLoginStore';

const DEFAULT_CONFIRM_TEXT = '下一步';

/**
 * 验证码（登录）相关主服务
 * @param type UI（业务）类型
 */
export function usePhoneLoginService(): PhoneLoginRequires {
  const toast = createCenterToast();
  const xCefUtil = useXCefUtil();
  const verify = useCaptcha('login');
  const router = useRouter();
  const { setCurrentLoginAccount } = useStore(CURRENT_LOGIN_STORE_KEY);
  const store = useStore(SMS_CODE_LOGIN_STORE_KEY);
  const { setPhoneLoginData } = store;

  const accountTracking = new AccountLoginTracking((p: any) =>
    xCefUtil?.dataAnalysisModule.report.bind(xCefUtil?.dataAnalysisModule)(p),
  );
  const opTracking = new AccountOperationTracking((p: any) =>
    xCefUtil?.dataAnalysisModule.report.bind(xCefUtil?.dataAnalysisModule)(p),
  );

  const autoLoginEntity = useAutoLoginStore();

  const exceptionService = useExceptionService();

  const [isAllowPolicy, PolicyCheckbox] = usePolicyChecker();
  const AutoLoginCheckbox = useAutoLoginChecker();

  const [loginBtnStatus, setLoginBtnStatus] = useState(() => ({
    text: DEFAULT_CONFIRM_TEXT,
    disabled: true,
  }));

  const [flashRef, flashAction] = useFlash();

  const {
    control,
    handleSubmit,
    watch,
    setFocus,
    setValue,
    formState: { errors },
  } = useForm<FormDto>({
    defaultValues: {
      account: '',
    },
  });

  // 进场时设置最近登录账号
  useSelectRecentAccount(setFocus, setValue);

  // 错误信息
  const errorMessage = Object.keys(errors).map(key => errors[key]?.message ?? '');

  // 更新按钮情况
  const accountField = watch().account;
  useEffect(() => {
    setLoginBtnStatus(prev => ({
      ...prev,
      disabled: !accountField,
    }));
  }, [accountField, watch]);

  const recentAccountEntity = useRecentAccountStore();
  const showLastLoggedinTips = useShowLastLoggedInTips(accountField, recentAccountEntity.query?.recentAccount);

  // 提交
  const onSubmit = async (data: FormDto) => {
    // 上报数据
    // 这个时机上报目的：可以收集到不同意协议的行为
    accountTracking.ssgLoginAccountCodeStart({
      preAccountId: secretStr(data.account),
      isAutoLogin: autoLoginEntity.query.state ? '1' : '0',
      isAgreeDeal: isAllowPolicy ? '1' : '0',
    });

    if (!isAllowPolicy) {
      flashAction();

      // 上报数据
      // 上报目的：有些人啊 你说你不同意协议怎么用产品
      accountTracking.ssgLoginAccountFail({
        preAccountId: secretStr(data.account),
        loginWay: 'email',
        code: -99999,
        statusReason: '未同意协议',
      });

      return;
    }

    setLoginBtnStatus({
      text: '正在登录',
      disabled: true,
    });

    const geeData = await verify();

    if (geeData.state !== CaptchaVerifyState.Pass) {
      setLoginBtnStatus({
        text: DEFAULT_CONFIRM_TEXT,
        disabled: false,
      });
      return;
    }

    const _data = new VerifyCodeDTO({
      captcha: geeData.data,
      channel: EChannel.LOGIN,
      verifyType: EVerifyType.SMS,
      account: data.account,
    });

    // 查询列表是否有被缓存的记录
    const getAccountAssociateMobilePhone = promisify(
      (xCefUtil as ExtEnvSDK).accountModule.getAccountAssociateMobilePhone.bind(xCefUtil?.accountModule),
    );
    const record = (await getAccountAssociateMobilePhone({
      accountOpenId: md5(data.account),
    })) as unknown as {
      /**
       * 哈希的手机号
       */
      hashedMobileNumber: string;
      /**
       * 打码的手机号, e.g.: 137***240
       */
      maskedMobileNumber: string;
    };

    if (record.hashedMobileNumber) {
      _data.contactWay = record.hashedMobileNumber;
    }

    try {
      await fetchVerifyCode(_data);
    } finally {
      setLoginBtnStatus({
        text: DEFAULT_CONFIRM_TEXT,
        disabled: false,
      });
    }
  };

  const fetchVerifyCode = async (data: VerifyCodeDTO) => {
    const request = await initRequest();
    const api = new SMSApi(request);
    try {
      const res = await api.getSMSVerifyCode(data);
      setPhoneLoginData({
        ...res,
        account: data.account,
        mobileOpenId: data.contactWay,
        type: 'login',
      });
      router.push('/phone/verify/login');
    } catch (e) {
      if (isNetworkError(e)) {
        return;
      }
      const error = e as ServerError;
      if (!(await exceptionService.phoneProcess(error, data, ELoginUIType.LOGIN))) {
        return;
      }
      toast(error?.msg);

      // 数据上报
      accountTracking.ssgLoginAccountFail({
        preAccountId: secretStr(data.account),
        loginWay: 'email',
        code: error?.code,
        statusReason: error?.msg,
      });
    }
  };

  const handleSwitchLogin = useCallback(
    (val: string) => () => {
      // 上报数据
      opTracking.ssgChooseLogin({
        loginWay: val,
      });
    },
    [opTracking],
  );

  return {
    control,
    PolicyCheckbox,
    AutoLoginCheckbox,
    loginBtnStatus,
    errorMessage: errorMessage.length ? errorMessage[0] : '',
    flashRef,

    onSubmit: handleSubmit(onSubmit),
    handleSwitchLogin,
    showLastLoggedinTips,
  };
}
