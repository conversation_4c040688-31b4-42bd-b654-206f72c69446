import { Controller } from 'react-hook-form';
import { InputEnhance, ConfirmButton, SelectEnhance } from '@ssglauncher/account/components';
import ContentLayout from '../../common/components/ContentLayout';
import { smsCodeValidRule } from '../../common/utils/validRule';
import { useRiskControlService } from '../services/riskControl.service';

function SecurityControl() {
  const service = useRiskControlService();
  const info = service.info;
  const TimerConfirmButton = service.Timer;

  return (
    <ContentLayout title="安全提示">
      <div className="mt-sp8">
        <div className="text-white-40 text-caption">检测到您的账号存在异常，请进行安全校验</div>
        <div className="mt-sp4 text-error text-head">
          <p>异常登录时间：{info.time}</p>
          <p>异常登录设备：{info.device}</p>
        </div>
        <div className="mt-sp36 text-white-100 text-head">账号：{info.maskAccount}</div>
        <form onSubmit={service.onSubmit} className="flex flex-col w-full mt-sp20">
          <div className="pb-sp20">
            <Controller
              name="type"
              control={service.control}
              render={({ field, fieldState: { error } }) => (
                <SelectEnhance {...field} options={service.optionList} error={!!error?.message} maxLength={10} />
              )}
            />
          </div>
          <div className="flex justify-between items-center">
            <div className="mr-sp8 min-w-[180px]">
              <Controller
                name="value"
                control={service.control}
                rules={smsCodeValidRule}
                render={({ field, fieldState: { error } }) => (
                  <InputEnhance
                    {...field}
                    error={!!error?.message}
                    icon="secure"
                    type="text"
                    placeholder="请输入验证码"
                    maxLength={6}
                  />
                )}
              />
            </div>
            {TimerConfirmButton}
          </div>

          <div className="h-sp36 text-white-80 text-caption">
            <div className="pt-sp12">{service.statusMsg}</div>
          </div>

          <div className="w-full">
            <ConfirmButton disabled={service.loginDisabled}>验证</ConfirmButton>
          </div>
        </form>
      </div>
      <div className="fixed bottom-sp20 left-0 w-full">
        <div className="max-w-screen-content mx-auto text-center">
          {/* <LinkWrapper href={contact.link} underline>
            {contact.name}
          </LinkWrapper> */}
        </div>
      </div>
    </ContentLayout>
  );
}

export default SecurityControl;
