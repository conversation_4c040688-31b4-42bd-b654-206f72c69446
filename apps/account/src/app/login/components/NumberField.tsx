import type { ForwardedRef } from 'preact/compat';
import { forwardRef, useCallback, useId, useState } from 'preact/compat';
import { clsx } from 'clsx';

interface NumberFieldProps {
  value: string;
  onChange?: (val: string) => void;
}

function NumberField({ value, onChange }: NumberFieldProps, ref: ForwardedRef<HTMLInputElement>) {
  const id = useId();

  const [focus, setFocus] = useState(false);

  const handleInput = e => {
    onChange?.(e.currentTarget.value);
  };

  const handleFocus = () => {
    setFocus(true);
  };

  const handleBlur = () => {
    setFocus(false);
  };

  const NumberItem = useCallback(
    ({ num, highlight }: { num: string; highlight: boolean }) => (
      <div className="flex flex-col mx-auto w-[42px] h-[60px] bg-white-8 rounded-small">
        <span
          className={clsx(
            'h-full text-[32px] flex justify-center items-center cursor-text border',
            highlight ? 'border-brand-default' : 'border-transparent',
          )}
        >
          {num}
        </span>
      </div>
    ),
    [],
  );

  return (
    <label htmlFor={id}>
      <input
        ref={ref}
        className="fixed w-1px h-1px opacity-[0]"
        id={id}
        value={value}
        maxLength={6}
        onChange={handleInput}
        onFocus={handleFocus}
        onBlur={handleBlur}
        autoFocus
      />
      <div className="flex justify-between">
        {Array(6)
          .fill('')
          .map((_, index) => (
            <div key={`number-item-${index}`} className="basis-[42px] relative text-center">
              <NumberItem num={value[index]} highlight={focus && index === value.length} />
            </div>
          ))}
      </div>
    </label>
  );
}

export default forwardRef<HTMLInputElement, NumberFieldProps>(NumberField);
