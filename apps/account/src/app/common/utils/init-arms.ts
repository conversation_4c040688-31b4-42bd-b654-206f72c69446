import ArmsRum from '@arms/rum-browser';

if (import.meta.env.PROD) {
  const SSG_RUM_PID = import.meta.env.SSG_RUM_PID;
  const SSG_RUM_ENDPOINT = import.meta.env.SSG_RUM_ENDPOINT;
  const SSG_RUM_VERSION = import.meta.env.SSG_RUM_VERSION;

  if (!SSG_RUM_PID) {
    console.warn('SSG_RUM_PID is missing');
  }
  if (!SSG_RUM_ENDPOINT) {
    console.warn('SSG_RUM_ENDPOINT is missing');
  }
  if (!SSG_RUM_VERSION) {
    console.warn('SSG_RUM_VERSION is missing');
  }

  if (SSG_RUM_PID && SSG_RUM_ENDPOINT) {
    ArmsRum.init({
      pid: SSG_RUM_PID,
      endpoint: SSG_RUM_ENDPOINT,
      version: SSG_RUM_VERSION,
      reportConfig: {
        flushTime: 1,
        maxEventCount: 50,
      },
    });
  }
}
