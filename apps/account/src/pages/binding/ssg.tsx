import type { AccountLoginUIProps } from '#app/login/views/AccountLogin';
import { useAccountBindService } from '#app/login/services/bind/accountBind.service';
import AccountLogin from '#app/login/views/AccountLogin';

const ui: AccountLoginUIProps = {
  tag: '绑定',
  title: '绑定金山通行证',
  showAutoChecker: false,
  confirmBtnText: '立即绑定',
  path: 'binding',
};

export function AccountBindingPage() {
  const service = useAccountBindService(ui.confirmBtnText);
  return <AccountLogin service={service} ui={ui} />;
}

export default AccountBindingPage;
