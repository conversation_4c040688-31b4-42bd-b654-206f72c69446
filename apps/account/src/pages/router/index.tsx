import type { To } from 'react-router-dom';
import { Navigate, RouterProvider, createBrowserRouter, useLocation } from 'react-router-dom';
import { App } from '../../app';
import { SSGLoginPage } from '../login/ssg';
import { phoneRouter } from './phone';
import { registerRouter } from './register';
import { BASE_PATH } from '#app/common/constants/appConfig';

// const createRouter = import.meta.env.DEV ? createBrowserRouter : createHashRouter;
const createRouter = createBrowserRouter;

function RedirectWithParams({ to }: { to: To }) {
  const location = useLocation();
  const searchParams = location.search; // 获取查询参数

  return <Navigate to={`${to}${searchParams}`} replace />;
}

// 为了兼容旧路径，所有页面path都加上了.html后缀
const router = createRouter(
  [
    {
      path: '/',
      element: <App />,
      // errorElement: null, // todo
      children: [
        {
          path: 'login',
          children: [
            {
              path: 'ssg.html',
              Component: SSGLoginPage,
            },
            {
              path: 'phone.html',
              lazy: async () => ({
                Component: (await import('../login/phone')).default,
              }),
            },
            {
              path: 'safe.html',
              lazy: async () => ({
                Component: (await import('../login/safe')).default,
              }),
            },
            {
              path: 'sec-ctrl.html',
              lazy: async () => ({
                Component: (await import('../login/sec-ctrl')).default,
              }),
            },
          ],
        },
        {
          path: 'binding',
          children: [
            {
              path: 'ssg.html',
              lazy: async () => ({
                Component: (await import('../binding/ssg')).default,
              }),
            },
            {
              path: 'phone.html',
              lazy: async () => ({
                Component: (await import('../binding/phone')).default,
              }),
            },
            {
              path: 'sec-ctrl.html',
              lazy: async () => ({
                Component: (await import('../binding/sec-ctrl')).default,
              }),
            },
          ],
        },
        phoneRouter,
        {
          path: 'register.html',
          lazy: async () => ({
            Component: (await import('../register/index')).default,
          }),
        },
        registerRouter,
      ],
    },
  ],
  { basename: BASE_PATH },
);

export function Router() {
  return <RouterProvider router={router} />;
}
