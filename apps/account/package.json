{"name": "ssglauncher-app-account", "private": true, "version": "1.8.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode prod", "build:prod": "vite build --mode prod", "preview": "vite preview"}, "dependencies": {"react": "npm:@preact/compat@^17.1.2", "react-dom": "npm:@preact/compat@^17.1.2", "preact": "10.19.3", "unocss": "^0.58.0", "@ssglauncher/components": "workspace:*", "@ssglauncher/sdk": "^2.3.29", "@ssglauncher/hooks": "workspace:*", "@ssglauncher/theme": "workspace:*", "@ssglauncher/server-api": "workspace:*", "@ssglauncher/account": "workspace:*", "@ssglauncher/iconfont": "workspace:*", "@ssglauncher/utils": "workspace:*", "@ssglauncher/l10n": "workspace:*", "@ssglauncher/tracking": "workspace:*", "@unocss/reset": "^0.58.0", "react-hook-form": "~7.53.1", "react-router-dom": "^6.20.1", "clsx": "^2.0.0", "jotai": "~2.8.0", "@xfe/captcha-handler": "1.2.6-5", "blueimp-md5": "^2.19.0", "@tuilan/jsutils": "^0.2.4", "usehooks-ts": "~3.1.0", "@arms/rum-browser": "~0.0.34", "@ssglauncher/biz-core": "workspace:*"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "~7.18.6", "@babel/plugin-proposal-decorators": "^7.23.5", "@preact/preset-vite": "^2.7.0", "@types/node": "^20.8.9", "babel-plugin-transform-typescript-metadata": "^0.3.2", "vite": "^5.0.7", "vite-imagetools": "^6.1.0", "unocss-preset-scrollbar-hide": "^1.0.1", "vite-plugin-top-level-await": "~1.4.4"}}