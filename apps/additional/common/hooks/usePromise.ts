import { useState, useEffect } from 'react';

interface PromiseResult<T, K> {
  data: T | null;
  loading: boolean;
  error: K | undefined;
}

function usePromise<T, K = undefined>(promiseFunction: () => Promise<T>): PromiseResult<T, K> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<K | undefined>(undefined);

  useEffect(() => {
    const makeAsync = async () => {
      try {
        setLoading(true);
        const result = await promiseFunction();
        setData(result);
      } catch (error) {
        setError(error as K);
      } finally {
        setLoading(false);
      }
    };

    makeAsync();
  }, [promiseFunction]);

  return { data, loading, error };
}

export default usePromise;
