import type { PropsWithChildren } from 'react';
import { Icon } from '@ssglauncher/iconfont';
import { clsx } from 'clsx';
import { type NavGroup, navAttrs } from '@ssglauncher/gamepad';
import SecondaryButton from '#common/components/SecondaryButton';
import { Gap } from '#common/components/Gap';
import ConfirmButton from '#common/components/ConfirmButton';

export interface WindowWrapperProps {
  leftButtonText?: string;
  rightButtonText?: string;
  className?: string;
  onLeftButtonDisabled?: boolean;
  onRightButtonDisabled?: boolean;
  navGroup?: NavGroup;
  hideCloseButton?: boolean;

  onLeftClick?(): void;

  onRightClick?(): void;
}

export function WindowWrapper({
  children,
  className,
  onLeftClick,
  onRightClick,
  leftButtonText = '取消',
  rightButtonText = '确认',
  onLeftButtonDisabled,
  onRightButtonDisabled,
  navGroup,
  hideCloseButton,
}: PropsWithChildren<WindowWrapperProps>) {
  const handleClose = () => {
    window.close();
  };

  const attrs = (index: number, esc?: boolean) =>
    navGroup
      ? navAttrs(navAttrs.index(navGroup, index), esc ? 'none' : 'x', esc ? { esc: 'click' } : undefined)
      : undefined;

  return (
    <div className={clsx('relative', className)}>
      <div className="absolute top-0 left-0 w-full p-sp12 flex justify-end items-center">
        <button {...attrs(2, true)} onClick={handleClose} className="flex">
          <Icon
            icon="close"
            className={clsx('text-size-18px color-white-60 hover:text-white-100', hideCloseButton && 'invisible')}
          />
        </button>
      </div>
      {children}
      {(onLeftClick || onRightClick) && (
        <div className="h-60px">
          <div className="flex justify-end absolute left-0 right-0 bottom-0 h-70px pt-18px pr-28px bg-black-94">
            <div className="bg-gradient-to-t from-[#1D1E1F] to-[#1d1e1f00] h-10px absolute top-0 left-0 right-0 pointer-events-none" />
            {onLeftClick && (
              <div className="w-108px h-32px">
                <SecondaryButton {...attrs(0)} onClick={onLeftClick} disabled={onLeftButtonDisabled}>
                  {leftButtonText}
                </SecondaryButton>
              </div>
            )}
            <Gap w={12} />
            {onRightClick && (
              <div className="w-108px h-32px">
                <ConfirmButton {...attrs(1)} onClick={onRightClick} disabled={onRightButtonDisabled}>
                  {rightButtonText}
                </ConfirmButton>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
