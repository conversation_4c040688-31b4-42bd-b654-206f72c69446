import { clsx } from 'clsx';
import LockableButton, { type LockableButtonProps } from './LockableButton';

type ConfirmButtonProps = LockableButtonProps;

function ConfirmButton({ className, children, ...rest }: ConfirmButtonProps) {
  return (
    <LockableButton
      className={clsx(
        'w-full h-sp32 select-none py-[6px] from-confirm-brand-left to-confirm-brand-right bg-gradient-to-r button-effects flex items-center justify-center rounded-small outline-none text-head text-white-100',
        className,
      )}
      {...rest}
    >
      {children}
    </LockableButton>
  );
}

export default ConfirmButton;
