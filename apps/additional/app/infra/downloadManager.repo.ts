import type { Observable } from 'rxjs';
import type { MainGameInfoRemote } from '@ssglauncher/server-api';
import { promisify, type XCefSDK } from '@ssglauncher/sdk';
import { Subject, AsyncSubject, of, tap, combineLatest } from 'rxjs';
import { listenerToObserver } from '@ssglauncher/operators';
import { from, lastValueFrom, map, shareReplay, switchMap } from 'rxjs';
import { BSRepository } from '@banshee/ex-banshee';
import { InjectPlugin, Repository } from '@banshee/ex-banshee';
import {
  DownloadState,
  EDownloadType,
  type IDownloadManagerRepo,
} from '#domains/downloadManager/repository/downloadManager.repo';
import { XCefSDKSymbol } from '#domains/infra/xcefSdk.plugin';
import { DownloadTaskEntity } from '#domains/downloadManager/model/DownloadTask.entity';
import { getAppEnv } from '#common/constant/appEnv';

type TXCef = Awaited<ReturnType<typeof XCefSDK>>;

@Repository('DownloadManager')
export class DownloadManagerRepo extends BSRepository implements IDownloadManagerRepo {
  private xcef$: AsyncSubject<TXCef> = new AsyncSubject();
  private currentGameStatus$: Subject<DownloadTaskEntity[]>;
  private currentPreGameStatus$: Subject<DownloadTaskEntity[]>;
  private gameCache: Map<string, MainGameInfoRemote>;

  constructor(
    @InjectPlugin(XCefSDKSymbol)
    xcefPlugin: typeof XCefSDK,
  ) {
    super();

    this.currentGameStatus$ = new Subject<DownloadTaskEntity[]>();
    this.currentPreGameStatus$ = new Subject<DownloadTaskEntity[]>();
    this.gameCache = new Map();
    this.createDownloadStatusListener();
    this.init(xcefPlugin);
  }

  currentGameListStatus() {
    return this.currentGameStatus$.asObservable();
  }

  currentPreGameListStatus() {
    return this.currentPreGameStatus$.asObservable();
  }

  async createPreTask(
    appCode: string,
    action: keyof Awaited<ReturnType<typeof XCefSDK>>['GameModule']['CreateGameTaskType'],
  ) {
    const xcef = await lastValueFrom(this.xcef$);

    xcef.gameModule.createGameTask({ appCode, action, isPreDownload: true });
  }

  private createDownloadStatusListener() {
    this.ready(xcef => listenerToObserver(xcef.gameModule.onGameDownloadStatusChanged))
      .pipe(
        switchMap(value =>
          combineLatest([
            this.getGameInfo(value.appCode),
            of(
              new DownloadTaskEntity({
                id: value.appCode,
                currentVersion: value.currentVersion,
                nextVersion: value.nextVersion,
                msg: value.msg,
                errMsg: value.errMsg,
                progress: value.progress,
                icon: `${getAppEnv()['SSG_LAUNCHER_STATIC_PREFIX']}/icon/game/${window.__XCEF_LANG__}/${value.appCode}`,
                ui: value.uiStage,
                isPreDownloadVisible: value.isPreDownloadVisible,
                name: '',
                downloadType: value.downloadType,
              }),
            ),
          ]),
        ),
        map(([gameInfo, entity]) => {
          entity.name = gameInfo.strAppName;
          return [entity];
        }),
      )
      .subscribe(v => {
        const [downloadItem] = v;
        if (downloadItem.downloadType === EDownloadType.Common) {
          this.currentGameStatus$.next(v);
        }
        if (downloadItem.downloadType === EDownloadType.PreDownload) {
          const list = v.filter(x => x.ui !== DownloadState.UNDEFINED && x.ui !== DownloadState.UN_DOWNLOADED);
          this.currentPreGameStatus$.next(list);
        }
      });
  }

  private getGameInfo(gameId: string) {
    return of(this.gameCache.get(gameId)).pipe(
      switchMap(cache =>
        !cache
          ? this.ready(xcef =>
              from(
                promisify(xcef.gameModuleData.getGameInfo.bind(xcef.gameModuleData))({
                  appCode: gameId,
                }) as Promise<MainGameInfoRemote>,
              ),
            ).pipe(
              tap(value => {
                this.gameCache.set(gameId, value);
              }),
            )
          : of(cache),
      ),
    );
  }

  private ready<T>(observer: (xcef: TXCef) => Observable<T>): Observable<T> {
    return this.xcef$.pipe(
      shareReplay(1),
      switchMap(xcef => observer(xcef)),
    );
  }

  private init(xcefPlugin: typeof XCefSDK) {
    from(xcefPlugin()).subscribe(this.xcef$);

    this.xcef$.subscribe(xcef => {
      this.windowActionTypes = xcef.GameModule.CreateGameTaskType;
    });
  }
}
