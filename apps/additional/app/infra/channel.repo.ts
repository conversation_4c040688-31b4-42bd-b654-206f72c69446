import type { ChannelConfig, IChannelRepo } from '#domains/channel/repository/channel.repo';
import type { XCefSDK } from '@ssglauncher/sdk';
import type { Observable } from 'rxjs';
import { BSRepository, InjectPlugin, Repository } from '@banshee/ex-banshee';
import { promisify } from '@ssglauncher/sdk';
import { BehaviorSubject, ReplaySubject, from, shareReplay, switchMap } from 'rxjs';
import { XCefSDKSymbol } from '#domains/infra/xcefSdk.plugin';

type TXCef = Awaited<ReturnType<typeof XCefSDK>>;

@Repository('Channel')
export class ChannelRepo extends BSRepository implements IChannelRepo {
  private xcef$: ReplaySubject<TXCef> = new ReplaySubject(1);
  private data$: BehaviorSubject<ChannelConfig>;

  constructor(
    @InjectPlugin(XCefSDKSymbol)
    xcefPlugin: typeof XCefSDK,
  ) {
    super();

    this.data$ = new BehaviorSubject<ChannelConfig>({
      AboutUsUrl: '',
      Channel: '',
      ConnectServiceUrl: '',
      HealthAdvice: '',
      Modules: {
        EnableAccountModule: false,
        EnableGameHomePage: false,
        EnableGameLeftBar: false,
        EnableLauncherChangeLanguage: false,
        EnableLauncherShowMenuEnter: false,
        EnableLauncherShowSplash: false,
        EnableMakeHealthAdvice: false,
        EnableReplaceEnToCn: false,
        EnableLauncherSettingCommonComprehensive: true,
        EnableLauncherSettingCommonLauncher: true,
        EnableLauncherSettingDownload: true,
        EnableLauncherSettingGameFolder: true,
        EnableLauncherSettingGameUpdate: true,
        EnableLauncherSettingGameLaunchOption: false,
        EnableLauncherSettingAboutOtherInformation: true,
        EnableLauncherSettingAboutRepairToolGuide: true,
        ShieldLangs: [],
        DefaultLang: '',
      },
    });

    this.init(xcefPlugin);
  }

  getConfig() {
    return this.data$.asObservable();
  }

  private init(xcefPlugin: typeof XCefSDK) {
    from(xcefPlugin()).subscribe(this.xcef$);

    this.ready(xcef =>
      from<Promise<ChannelConfig>>(
        promisify(xcef.basicModuleData.getChannelRemoteConfigData.bind(xcef.basicModuleData))(),
      ),
    )

      .pipe(shareReplay(1))
      .subscribe(v => this.data$.next(v));
  }

  private ready<T>(observer: (xcef: TXCef) => Observable<T>): Observable<T> {
    return this.xcef$.pipe(
      shareReplay(1),
      switchMap(xcef => observer(xcef)),
    );
  }
}
