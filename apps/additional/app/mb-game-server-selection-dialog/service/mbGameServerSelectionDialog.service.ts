import type { IMBServerTranslation, IMBServerWithPing } from '#domains/mbServerSelector/model/mbServerPingList.entity';
import type { Subscription } from 'rxjs';
import { useTranslation } from 'react-i18next';
import { useService, useStream } from '@banshee/banshee-preact';
import { useEffect, useState } from 'react';
import { promisify } from '@ssglauncher/sdk';
import { useCallback } from 'preact/hooks';
import { getLang } from '@ssglauncher/utils';
import { sleep } from '@tuilan/asuka-request';
import { AboutGameTracking } from '@ssglauncher/tracking';
import { mb } from '@ssglauncher/biz-core/game-server';
import { MbServerListService } from '#domains/mbServerSelector/service/mbServerList.service';
import { logger } from '#common/utils/logger';

export const useMBGameServerSelectionDialog = () => {
  const { t } = useTranslation();
  const { searchParams } = new URL(location.href);
  const appCode = searchParams.get('appCode');
  const mbServerListService = useService(MbServerListService);
  const lastSelectedServer = useStream(() => mbServerListService.getLastSelectedServer());
  const reducedServerPingList = useStream(() => mbServerListService.getReducedServerPingList());
  const [currentSelectedServer, setCurrentSelectedServer] = useState<IMBServerWithPing | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const isFetchError = useStream(() => mbServerListService.getIsFetchError());
  const localeToName = (locales?: IMBServerTranslation[]) => locales?.find(v => v.lang === getLang())?.text || '';
  const mbGameService = mb.useGameServerService();

  const closeWebDialog = promisify(params => (window as any).xcef.uiModule.closeWebDialog(params));

  const onCancelClick = useCallback(async () => {
    await closeWebDialog({
      type: (window as any).xcef.UiModule.CloseWebDialogType.CloseDialog,
      payload: null,
    });
  }, []);

  const onConfirmClick = useCallback(async () => {
    logger.error('[server][selectMBServer][onConfirm]', { server: currentSelectedServer, appCode: appCode });
    if (!currentSelectedServer || !appCode) {
      return;
    }
    await mbGameService.selectMbServer({
      server: currentSelectedServer,
      appCode: appCode,
    });
    new AboutGameTracking((p: any) => (window as any).xcef.dataAnalysisModule.report(p)).ssgSeverSelect({
      presentSeverChoose: currentSelectedServer.key,
    });
    await sleep(500);
    await closeWebDialog({
      type: (window as any).xcef.UiModule.CloseWebDialogType.CloseDialog,
      payload: null,
    });
  }, [currentSelectedServer, appCode]);

  const onServerSelectionChange = useCallback((server: IMBServerWithPing | null) => {
    setCurrentSelectedServer(server);
  }, []);

  let sub: Subscription;
  const fetchMBServerPingList = useCallback(() => {
    if (sub) {
      sub.unsubscribe();
    }
    if (appCode) {
      sub = mbServerListService.fetchMBServerPingList(appCode).subscribe();
    }
  }, [appCode, mbServerListService.fetchMBServerPingList]);

  useEffect(() => {
    fetchMBServerPingList();
    return () => {
      sub?.unsubscribe();
    };
  }, [fetchMBServerPingList]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (reducedServerPingList || isFetchError) {
        setLoading(false);
      }
    }, 500); // 设置1秒的默认延迟防止loading闪动

    return () => {
      clearTimeout(timer);
    };
  }, [reducedServerPingList, isFetchError]);

  // 默认选中（上次选择）区服
  useEffect(() => {
    if (lastSelectedServer && !currentSelectedServer) {
      setCurrentSelectedServer(lastSelectedServer);
    }
  }, [lastSelectedServer, !currentSelectedServer]);

  return {
    currentSelectedServer,
    title: `${t('GAME_SERVER_SELECTION.SELECT_SERVER', '选择区服')}`,
    subTitle: lastSelectedServer?.lastSelected
      ? t('ADDITIONAL_SERVER_SELECTION:LAST_SELECTION', '上次选择')
      : t('ADDITIONAL_SERVER_SELECTION:CURRENT_RECOMMENDATION', '当前推荐'),
    thirdTitle: t('ADDITIONAL_SERVER_SELECTION:ALL_SERVERS', '全部区服'),
    leftButtonText: t('ADDITIONAL_SERVER_SELECTION:CANCEL_BUTTON_TEXT', '取消'),
    rightButtonText: t('ADDITIONAL_SERVER_SELECTION:CONFIRM_BUTTON_TEXT', '确认'),
    lastSelectedServer,
    reducedServerPingList,
    serverActiveId: lastSelectedServer?.key || currentSelectedServer?.key,
    localeToName,
    loading,
    loadingMessage: t('QUI_XWEBVIEW_LOADING:LOADING_TEXT', '加载中...'),
    onCancelClick,
    onConfirmClick,
    onServerSelectionChange,
    isFetchError,
    refresh: fetchMBServerPingList,
  };
};
