import clsx from 'clsx';

import LoadingImg from './loading.webp';

export interface LoadingProps {
  loading?: boolean;
  message?: string;
}

export function Loading({ className, loading, message }: LoadingProps) {
  if (!loading) return null;
  return (
    <div className={clsx('absolute inset-0 z-50 flex flex-col justify-center items-center bg-black-94')}>
      <img src={LoadingImg} className="w-67px h-67px" />
      {!!message && <div className="mt-1 text-white-100 text-body">{message}</div>}
    </div>
  );
}
