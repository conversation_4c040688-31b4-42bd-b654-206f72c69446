import type { Observable } from 'rxjs';
import type { IMBServerWithPing } from '#domains/mbServerSelector/model/mbServerPingList.entity';

export abstract class IMBServerListRepo {
  abstract getLastSelectedServer(): Observable<IMBServerWithPing | null>;
  abstract getReducedServerPingList(): Observable<IMBServerWithPing[] | null>;
  abstract fetchMBServerPingList(gameId: string): Observable<void>;
  abstract getIsFetchError(): Observable<boolean>;
}
