import type { IMBServerListRepo } from '#domains/mbServerSelector/repository/mbServerList.repo';
import type { XCefSDK } from '@ssglauncher/sdk';
import type { Observable } from 'rxjs';
import { BSRepository, InjectPlugin, Repository } from '@banshee/ex-banshee';
import { promisify } from '@ssglauncher/sdk';
import {
  BehaviorSubject,
  catchError,
  concatMap,
  from,
  map,
  of,
  ReplaySubject,
  shareReplay,
  switchMap,
  tap,
} from 'rxjs';
import { listenerToObserver } from '@ssglauncher/operators';
import {
  createGameServerListData,
  type IMBRegionsWithPing,
  type GameServerListData,
  type IMBServerWithPing,
  type MBServerListRemote,
  type MbServerPingListEntity,
  type IMBServer,
} from '#domains/mbServerSelector/model/mbServerPingList.entity';
import { XCefSDKSymbol } from '#domains/infra/xcefSdk.plugin';
import { logger } from '#common/utils/logger';

type TXCef = Awaited<ReturnType<typeof XCefSDK>>;

type MBSelectedServerChangedCallback = Parameters<TXCef['gameModule']['onMBSelectedServerChanged']>[0];
type MBSelectedServerChangedCallbackParams = Parameters<MBSelectedServerChangedCallback>[0] | undefined;

type TMBServerPingList = MbServerPingListEntity | undefined | null;

@Repository('MBServerListRepo')
export class MBServerListRepo extends BSRepository implements IMBServerListRepo {
  private xcef$: ReplaySubject<TXCef> = new ReplaySubject(1);
  private reducedServerPingList$: BehaviorSubject<IMBServerWithPing[] | null> = new BehaviorSubject<
    IMBServerWithPing[] | null
  >(null);
  private lastSelectedServer$: BehaviorSubject<IMBServerWithPing | null> =
    new BehaviorSubject<IMBServerWithPing | null>(null);
  /** 拉取区服时是否出错 */
  private isFetchError$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  constructor(
    @InjectPlugin(XCefSDKSymbol)
    xcefPlugin: typeof XCefSDK,
  ) {
    super();
    this.init(xcefPlugin);
    this.createMBSelectedServerChangedListener();
  }

  getLastSelectedServer(): Observable<IMBServerWithPing | null> {
    return this.lastSelectedServer$.asObservable();
  }

  getReducedServerPingList(): Observable<IMBServerWithPing[] | null> {
    return this.reducedServerPingList$.asObservable();
  }

  getIsFetchError(): Observable<boolean> {
    return this.isFetchError$.asObservable();
  }

  fetchMBServerPingList(gameId: string) {
    return this.ready(xcef => {
      this.isFetchError$.next(false);
      return from(promisify(xcef.gameModuleData.getMBServerList.bind(xcef.gameModuleData))({ appCode: gameId })).pipe(
        tap((v: MBServerListRemote) => {
          logger('[server][getMBServerList]', {
            regions: v?.regions?.map(region => region.id) || [],
            servers: v?.servers?.map(server => server.id) || [],
          });
        }),
        map<MBServerListRemote, GameServerListData>((res: MBServerListRemote) => {
          return createGameServerListData(res);
        }),
        concatMap((serversData: GameServerListData) =>
          from(promisify(xcef.gameModuleData.getMBServerPingList.bind(xcef.gameModuleData))({ appCode: gameId })).pipe(
            map((regions: IMBRegionsWithPing[]) => ({ ...serversData, regions })),
          ),
        ),
        map(v => {
          this.reducedServerPingList$.next(this.getRegionLowestPing(v));
        }),
        catchError(() => {
          this.isFetchError$.next(true);
          return of();
        }),
      );
    });
  }

  private getRegionLowestPing(serverPingList: TMBServerPingList): IMBServerWithPing[] {
    const serverListWithLowestPingRegion: IMBServerWithPing[] = [];

    serverPingList?.servers?.forEach((server: IMBServer) => {
      const selectedRegions = server.regions.map(regionId =>
        serverPingList?.regions.find(region => region.id === regionId),
      );
      const minPing = Math.min(...(selectedRegions.map(region => (region ? region.ping : 500)) as number[]));
      const lastSelectedServer: IMBServerWithPing = {
        ...server,
        ping: minPing,
      };
      serverListWithLowestPingRegion.push(lastSelectedServer);
    });
    const lastSelected = serverListWithLowestPingRegion.find(v => v.lastSelected);

    if (lastSelected) {
      this.lastSelectedServer$.next(lastSelected);
    } else {
      this.lastSelectedServer$.next(
        serverListWithLowestPingRegion.reduce(
          (prevMin, current) => (current.ping < prevMin.ping ? current : prevMin),
          serverListWithLowestPingRegion[0],
        ),
      );
    }

    return serverListWithLowestPingRegion.sort((a, b) => a.ping - b.ping);
  }

  private init(xcefPlugin: typeof XCefSDK): void {
    from(xcefPlugin()).subscribe({
      next: xcef => this.xcef$.next(xcef),
    });
  }

  private ready<T>(observer: (xcef: TXCef) => Observable<T>): Observable<T> {
    return this.xcef$.pipe(
      shareReplay(1),
      switchMap(xcef => observer(xcef)),
    );
  }

  private createMBSelectedServerChangedListener(): void {
    this.ready(xcef => listenerToObserver<IMBServerWithPing>(xcef.gameModule.onMBSelectedServerChanged)).subscribe(
      newServer => {
        logger('[server][onMBSelectedServerChanged]', newServer);
        this.lastSelectedServer$.next(newServer);
      },
    );
  }
}
