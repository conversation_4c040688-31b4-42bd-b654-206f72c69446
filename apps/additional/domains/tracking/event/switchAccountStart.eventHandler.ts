import type { AccountManagerTracking } from '@ssglauncher/tracking';
import { EventHandler, type IBSEventHandler } from '@banshee/ex-banshee';
import { SwitchAccountStartEvent } from '#domains/accountManager/event/switchAccount/start.event';

@EventHandler(SwitchAccountStartEvent)
export class SwitchAccountStartEventHandler implements IBSEventHandler<SwitchAccountStartEvent> {
  constructor(private readonly tracking: AccountManagerTracking) {}

  handle(event: SwitchAccountStartEvent): void {
    console.log('ddd switch account event: ', event);
    this.tracking.ssgClickManageDeviceDetail({
      clickType: '切换账号',
      aftertAccountId: event.account.account,
      aftertAccountOpenId: event.account.openId,
    });
    console.log('ddd handle finish');
  }
}
