import type { AccountManagerTracking } from '@ssglauncher/tracking';
import { EventHandler, type IBSEventHandler } from '@banshee/ex-banshee';
import { SwitchAccountCancelEvent } from '#domains/accountManager/event/switchAccount/cancel.event';

@EventHandler(SwitchAccountCancelEvent)
export class SwitchAccountCancelEventHandler implements IBSEventHandler<SwitchAccountCancelEvent> {
  constructor(private readonly tracking: AccountManagerTracking) {}

  handle(event: SwitchAccountCancelEvent): void {
    this.tracking.ssgCanceChangeAccount({
      preAccountId: event.account.account,
    });
  }
}
