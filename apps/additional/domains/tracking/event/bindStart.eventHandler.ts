import type { AccountLoginTracking, AccountManagerTracking } from '@ssglauncher/tracking';
import { EventHandler, type IBSEventHandler } from '@banshee/ex-banshee';
import { BindStartEvent } from '#domains/accountManager/event/bind/bindStart.event';

@EventHandler(BindStartEvent)
export class BindStartEventHandler implements IBSEventHandler<BindStartEvent> {
  constructor(
    private readonly accountManagerTracking: AccountManagerTracking,
    private readonly accountLoginTracking: AccountLoginTracking,
  ) {}

  handle(event: BindStartEvent): void {
    this.accountManagerTracking.ssgClickManageDeviceDetail({
      clickType: '添加账号',
      aftertAccountId: '',
      aftertAccountOpenId: '',
    });

    this.accountLoginTracking.ssgAddAccountStart({
      aftertAccountId: event.currentAccount.account,
      aftertAccountOpenId: event.currentAccount.openId,
    });
  }
}
