import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

const prodType = import.meta.env.SSG_PROD_TYPE;
const isGP = prodType === 'gp';

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    ns: [
      'common',
      'ADDITIONAL',
      'ADDITIONAL_SETTINGS',
      'ADDITIONAL_SERVER_SELECTION',
      'ADDTIONAL_CUSTOMER_SERVICE',
      'ADDITIONAL_UPLOAD_LOG',
      'QUI_X_INSTALL_PAGE',
      'QUI_X_SETTING_GAMES_PAGE_ITEM',
      'QUI_X_SETTING_GAMES_PAGE',
      'QUI_X_SETTING_COMMON_PAGE',
      'QUI_X_SETTING_ABOUT_PAGE',
      'QUI_X_SETTING_DOWNLOAD_PAGE',
      'QUI_X_SETTING_COMMON_PAGE_STORE',
      'QUI_X_TAKE_OVER_PAGE',
      'QUI_XWEBVIEW_LOADING',
    ],
    fallbackNS: false,
    defaultNS: 'common',
    load: 'currentOnly',
    lng: isGP ? 'en_US' : 'zh_CN',
    fallbackLng: isGP ? 'en_US' : 'zh_CN',

    interpolation: {
      escapeValue: false,
    },

    react: {
      useSuspense: true,
    },

    backend: {
      loadPath: `${
        import.meta.env.PROD ? `https://launcher-static.xoyo.com/locales` : import.meta.env.SSG_I18N_BACKEND_URL
      }/{{lng}}/{{ns}}.json`,
      reloadInterval: false,
    },
  });

export default i18n;
