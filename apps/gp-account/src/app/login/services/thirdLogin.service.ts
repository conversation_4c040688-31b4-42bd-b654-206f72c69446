import type { LoginWay } from '../models/loginWay';
import { useCallback, useEffect, useState } from 'preact/hooks';
import { AccountLoginTracking } from '@ssglauncher/tracking';
import { createCenterToast } from '@ssglauncher/components';
import { setTraceIdExt } from '#app/common/repositories/traceId.repo';
import { GpAccount } from '#app/common/utils/gpAccount';
import { useAutoLoginStore } from '#app/common/store/autoLogin';
import { getLoginRedirectUrl } from '#app/login/repositories/thirdLogin.repo';

const toast = createCenterToast();
export function useThirdLoginService() {
  const autoLoginStore = useAutoLoginStore();
  const isAuthRemember = autoLoginStore.query.state;
  const [isThirdLogingIn, setIsThirdLogingIn] = useState(false);
  const [isThirdLogInTimeout, setIsThirdLogInTimeout] = useState(false);

  const openThirdPartyLoginLink = useCallback(
    (loginWay: LoginWay) => async () => {
      new AccountLoginTracking((p: any) => window.xcef.dataAnalysisModule.report(p)).ssgAccountLoginChannel({
        loginWay,
      });
      setIsThirdLogingIn(true);
      const redirectUrl = await getLoginRedirectUrl({ isAuthRemember });
      try {
        const gpAccount = await GpAccount.getInstance();
        const res = await gpAccount.getThirdLoginUrl({ loginType: loginWay, redirectUrl, isAuthRemember });
        console.log('xxx trace id: ', res.traceId);
        await setTraceIdExt(res.traceId);
        window.open(res.url, '__blank', 'noreferrer');
      } catch (e) {
        e?.msg && toast(e.msg);
        console.error('get third login url error', e);
      }
    },
    [isAuthRemember],
  );

  useEffect(() => {
    if (isThirdLogingIn) {
      setTimeout(() => {
        setIsThirdLogInTimeout(true);
        // 第三方登录超时时间为1分钟
      }, 60 * 1000);
    }
  }, [isThirdLogingIn]);

  function cancelThirdLogin() {
    setIsThirdLogingIn(false);
  }

  function closeThirdLoginTimeout() {
    setIsThirdLogInTimeout(false);
    setIsThirdLogingIn(false);
  }

  return {
    isThirdLogingIn,
    isThirdLogInTimeout,
    cancelThirdLogin,
    closeThirdLoginTimeout,
    openThirdPartyLoginLink,
  };
}
