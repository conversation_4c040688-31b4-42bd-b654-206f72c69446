import { createRedirectUrl, type RedirectUrlParams } from '#app/login/utils/third-login';
import { getChannelConfig } from '#app/common/repositories/channelConfig.repo';

/** 获取登录中转页地址 */
export async function getLoginRedirectUrl({
  isAuthRemember,
  canKickLogin,
}: Omit<RedirectUrlParams, 'appName'>): Promise<string> {
  const channelConfig = await getChannelConfig();
  const appName = channelConfig.UrlProtocol;
  const redirectUrl = createRedirectUrl({ appName, isAuthRemember, canKickLogin });
  return redirectUrl;
}
