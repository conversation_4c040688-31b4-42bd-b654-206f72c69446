import { RegisterApi, type GetRegisterUrlDTO } from '@ssglauncher/server-api';
import { initRequest } from '#app/common/utils/request';
import { getStaticRegistryUrl } from '#app/common/utils/accountWebsite';
import { setTraceIdExt } from '#app/common/repositories/traceId.repo';
import { getLoginRedirectUrl } from '#app/login/repositories/thirdLogin.repo';

export async function getRegisterConfig({
  isAuthRemember,
}: Pick<GetRegisterUrlDTO, 'isAuthRemember'>): Promise<{ url: string }> {
  const request = await initRequest();
  const api = new RegisterApi(request);

  const redirectUrl = await getLoginRedirectUrl({ isAuthRemember, canKickLogin: false });
  try {
    const res = await api.getRegisterUrl(
      {
        redirectUrl,
        isAuthRemember,
      },
      {
        timeout: 1500, // 因为要尽快响应用户点击，产品将超时时长暂定为1.5秒
      },
    );
    await setTraceIdExt(res.traceId);
    return res;
  } catch (e) {
    return { url: getStaticRegistryUrl() };
  }
}
