{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".",
    "strict": true,
    "module": "ESNext",
    "target": "ESNext",
    "moduleResolution": "bundler",
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "noEmit": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "types": ["vite/client"],
    "paths": {
      "react": ["node_modules/preact/compat"],
      "react-dom": ["node_modules/preact/compat"],
      "#app/*": ["./src/app/*"],
    }
  },
  "ts-node": {
    "swc": true,
    "transpileOnly": true,
    "esm": true,
    "compilerOptions": {
      "moduleResolution": "Node16"
    }
  }
}

