import type { BSEventSource } from '@banshee/ex-banshee';
import { GameZoneTracking } from '@ssglauncher/tracking';
import { BSBuiltInPlugins, BSService, InjectPlugin, JustService } from '@banshee/ex-banshee';
import { GameRecordTabClickedEventHandler } from '../event/gameRecordTabClicked.event';
import { GameRecordModeClickedEventHandler } from '../event/gameRecordModeClicked.event';

@JustService()
export class GameRecordTrackingService extends BSService {
  constructor(
    @InjectPlugin(BSBuiltInPlugins.EventStore)
    eventStore: BSEventSource,
  ) {
    super(eventStore);
    const report = window.xcef.dataAnalysisModule.report.bind(window.xcef.dataAnalysisModule);

    const tracking = new GameZoneTracking(report);

    this.registerEventHandler(
      [GameRecordTabClickedEventHandler, { params: [tracking] }],
      [GameRecordModeClickedEventHandler, { params: [tracking] }],
    );
  }
}
