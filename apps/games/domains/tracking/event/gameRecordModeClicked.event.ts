import type { IBSEventHandler } from '@banshee/ex-banshee';
import type { GameZoneTracking } from '@ssglauncher/tracking';
import { EventHandler } from '@banshee/ex-banshee';
import { GameRecordModeClickedEvent } from '#domains/zone/event/gameRecordModeClicked.event';

@EventHandler(GameRecordModeClickedEvent)
export class GameRecordModeClickedEventHandler implements IBSEventHandler<GameRecordModeClickedEvent> {
  constructor(private readonly tracking: GameZoneTracking) {}

  handle(event: GameRecordModeClickedEvent) {
    this.tracking.ssgClickGameMode({
      navListCat: event.gameCode,
      mode: event.modeName,
    });
  }
}
