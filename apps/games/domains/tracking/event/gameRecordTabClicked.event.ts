import type { IBSEventHandler } from '@banshee/ex-banshee';
import type { GameZoneTracking } from '@ssglauncher/tracking';
import { EventHandler } from '@banshee/ex-banshee';
import { GameRecordTabClickedEvent } from '#domains/zone/event/gameRecordTabClicked.event';

@EventHandler(GameRecordTabClickedEvent)
export class GameRecordTabClickedEventHandler implements IBSEventHandler<GameRecordTabClickedEvent> {
  constructor(private readonly tracking: GameZoneTracking) {}

  handle(event: GameRecordTabClickedEvent) {
    this.tracking.ssgClickMatchPage({
      navListCat: event.gameCode,
      title: event.title,
    });
  }
}
