import type { EUniversalNavigator } from '@ssglauncher/server-api';
import type { AUniversalNavigatorService } from '../service/universalNavigator.service';
import type { UniversalActionVO } from './universalAction.vo';
import type { BSEventStore } from '@banshee/ex-banshee';
import { BaseEntity } from './base.entity';
import { ContactClickedEvent } from '../event/contactClicked.event';

interface IContactEntity {
  id: string;
  gameCode: string;
  position: string;
  icon: string;
  order: number;
  details: ContactVO[];
}

interface ContactVO {
  name: string;
  qrCode: string;
  order: number;
}

export class ContactEntity extends BaseEntity implements IContactEntity, UniversalActionVO {
  readonly id: string;
  readonly gameCode: string;
  readonly position: string;
  readonly icon: string;
  readonly order: number;
  readonly details: ContactVO[];
  readonly srcType?: EUniversalNavigator;
  readonly srcValue?: string;

  constructor({ id, gameCode, position, icon, order, details, srcType, srcValue }: IContactEntity & UniversalActionVO) {
    super();
    this.id = id;
    this.gameCode = gameCode;
    this.position = position;
    this.icon = icon;
    this.order = order;
    this.details = details;
    this.srcType = srcType;
    this.srcValue = srcValue;
  }

  navigate(eventStore: BSEventStore, universalNavigatorService: AUniversalNavigatorService) {
    const action = {
      srcType: this.srcType,
      srcValue: this.srcValue,
    };

    this.handleEvent(eventStore);
    universalNavigatorService.execute(action, this.gameCode);
  }

  private handleEvent(eventStore: BSEventStore) {
    eventStore.emit(new ContactClickedEvent(this.gameCode, this.position));
  }
}
