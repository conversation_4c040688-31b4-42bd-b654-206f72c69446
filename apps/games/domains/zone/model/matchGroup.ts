import type { GameCampRemote, PointItemRemote } from '@ssglauncher/server-api';
import type { MatchPlayer } from './matchPlayer';
import type { PointDetail } from '#domains/record-data/models/pointDetail';
import { PlayType } from '@ssglauncher/server-api';
import { toLocalMatchPlayer } from './matchPlayer';
import { toLocalPointItem } from '#domains/record-data/models/pointItem';
import { createPointDetail } from '#domains/record-data/models/pointDetail';

/** 对局阵容 */
export interface MatchGroup extends Pick<GameCampRemote, 'result'> {
  /** 对局阵容id */
  id: string;
  /** 玩家 */
  players: MatchPlayer[];
  /** 得分项详细, 目前玛什马克才有 */
  pointDetail?: PointDetail;
}

export function toLocalMatchGroup(remote: GameCampRemote, playType: PlayType): MatchGroup {
  const matchGroup: MatchGroup = {
    ...remote,
    players: remote.players?.map(toLocalMatchPlayer),
  };
  if (playType === PlayType.MashMark) {
    const pointDetail = createPointDetail();
    Object.keys(pointDetail).forEach(k => {
      let key = k as keyof PointDetail;
      const value = remote[key as any as keyof GameCampRemote] as PointItemRemote;
      if (value) {
        pointDetail[key] = toLocalPointItem(value);
      }
    });
    matchGroup.pointDetail = pointDetail;
  }
  return matchGroup;
}
