export { createGameServerListData } from '@ssglauncher/biz-core/game-server/mb';

export interface IMBRegions {
  id: number;
  /** 节点ip */
  ip: string;
}

export interface IMBServerTranslation {
  /** 区服对应语言类型 */
  lang: string;
  /** 区服对应语言的名称 */
  text: string;
}

/** 区服原始数据 */
export interface OriginalGameServerRemote {
  /** 区服id */
  id?: string;
  /** 区服key，同区服id, 已废弃，但需要继续兼容旧区服json */
  key?: string;
  /** 区服翻译信息组 */
  name: IMBServerTranslation[];
  /** 区服节点组 */
  regions: number[];
  /** 区服类型 */
  netlink: string;
  /** 区服ip */
  ip: string;
  /** 区服状态 */
  status: number;
  /** 是否为上次选中的区服 */
  lastSelected?: boolean;
}

export interface IMBServerConfigs {
  /** 区服端口 */
  portalPort: number;
  /** ping端口 */
  pingPort: number;
  /** 区服默认语言类型 */
  defaultLanguage: string;
}

/** gameModuleData.getMBServerList 返回的数据 */
export interface MBServerListRemote {
  servers: OriginalGameServerRemote[];
  regions: IMBRegions[];
  config: IMBServerConfigs;
}

/** 转换过的区服列表数据(前端使用)  */
export interface GameServerListData extends Omit<MBServerListRemote, 'servers'> {
  servers: MbServerListEntity[];
}

export interface MbServerListEntity {
  /** 区服唯一标识，即id或key字段, 优先取id */
  key: string;
  /** 区服翻译信息组 */
  name: IMBServerTranslation[];
  /** 区服节点组 */
  regions: number[];
  /** 区服类型 */
  netlink: string;
  /** 区服ip */
  ip: string;
  /** 区服状态 */
  status: number;
  /** 是否为上次选中的区服 */
  lastSelected?: boolean;
}
