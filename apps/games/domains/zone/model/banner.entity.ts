import type { AUniversalNavigatorService } from '../service/universalNavigator.service';
import type { EUniversalNavigator } from '@ssglauncher/server-api';
import type { UniversalActionVO } from './universalAction.vo';
import type { BSEventStore } from '@banshee/ex-banshee';
import { BannerClickedEvent } from '../event/bannerClicked.event';
import { BaseEntity } from './base.entity';

interface IBannerEntity {
  id: string;
  /** 游戏 id */
  gameCode: string;
  /** 标题 */
  title: string;
  /** 图片 */
  image: string;
}

export class BannerEntity extends BaseEntity implements IBannerEntity, UniversalActionVO {
  readonly id: string;
  readonly gameCode: string;
  readonly title: string;
  readonly image: string;
  readonly srcType?: EUniversalNavigator;
  readonly srcValue?: string;

  constructor({ id, gameCode, title, image, srcType, srcValue }: IBannerEntity & UniversalActionVO) {
    super();
    this.id = id;
    this.gameCode = gameCode;
    this.title = title;
    this.image = image;
    this.srcType = srcType;
    this.srcValue = srcValue;
  }

  navigate(eventStore: BSEventStore, universalNavigatorService: AUniversalNavigatorService) {
    const action = {
      srcType: this.srcType,
      srcValue: this.srcValue,
    };

    this.handleEvent(eventStore, action);
    universalNavigatorService.execute(action, this.gameCode);
  }

  private handleEvent(eventStore: BSEventStore, action: UniversalActionVO) {
    eventStore.emit(new BannerClickedEvent(this.gameCode, action));
  }
}
