import type { GameBattleRecordRemote } from '@ssglauncher/server-api';
import { toLocalMatch, type Match } from '#domains/zone/model/match';

export interface MatchRecord extends Pick<GameBattleRecordRemote, 'more'> {
  data: Match[];
}

/** 游戏赛季 */
export interface GameSeason {
  /** 赛季id */
  id: number;
  /** 赛季名称 */
  name: string;
}

/** 战绩-赛季数据 */
export interface SeasunData {
  /** 驾驶时长 */
  drivingTime: string;
  /** 总场次 */
  matchCount: number;
  /** 成功撤离数 */
  successfulEvacuation: number;
  /** 荣誉 */
  honors: Honor[];
  /** 场均击毁数 */
  avgKills: number;
  /** 场均助攻数 */
  avgAssists: number;
  /** 场均坠毁数 */
  avgDeadths: number;
  /** 最高功勋分 */
  maxPoints: number;
  /** 最高击毁数 */
  maxKills: number;
  /** 最高助攻数 */
  maxAssists: number;
  /** 最高坠毁数 */
  maxDeadths: number;
  /** 最高伤害 */
  maxDamage: number;
  /** 最高维修 */
  maxHeal: number;
  /** 场均功勋分 */
  avgPoints: number;
  /** 场均伤害 */
  avgDamage: number;
  /** 场均维修 */
  avgHeal: number;
}

interface Honor {
  /** 荣誉类型 */
  type: HonorType;
  /** 荣誉数量 */
  count: number;
}

/** 荣誉类型 */
enum HonorType {
  /** 全场最佳 */
  Best,
  /** 金功勋章 */
  Golden,
  /** 银功勋章 */
  Silver,
  /** 铜功勋章 */
  Copper,
  /** 精英击杀数量 */
  EliteKills,
  /** boss击杀数量 */
  SpecialTargetKills,
  /** 特殊目标摧毁数量 */
  BossKills,
  /** boss击杀数量 */
}

export function toLocalGameRecord(remote?: GameBattleRecordRemote): MatchRecord {
  if (!remote) {
    return {
      more: false,
      data: [],
    };
  }
  return {
    more: remote.more,
    data: remote.data?.map(toLocalMatch) || [],
  };
}
