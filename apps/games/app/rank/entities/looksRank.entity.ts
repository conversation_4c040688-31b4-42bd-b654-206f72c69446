import type { LooksRankRemote } from '@ssglauncher/server-api';
import type { LooksRank } from '../models/looksRank.model';
import { formatDate, toCommonNumber } from '#app/data/utils/formatter';

export function toLocalLooksRank(remote: LooksRankRemote): LooksRank {
  return {
    ...remote,
    publishTimeDisp: formatDate(remote.saleTime * 1000),
    saleCountDisp: toCommonNumber(remote.saleCount ?? 0),
  };
}
