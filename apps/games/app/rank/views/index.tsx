import { LooksRank } from '../components/Looks/LooksRank';
import BgImg from '#app/rank/assets/bg.webp';
import BgDecoBlImg from '#app/rank/assets/bg-deco-bl.webp';
import BgDecoTrImg from '#app/rank/assets/bg-deco-tr.webp';

export function Rank() {
  return (
    <div
      className="relative w-full h-full"
      style={{ background: `url(${BgImg}) no-repeat`, backgroundSize: '100% 100%' }}
    >
      <div className="absolute top-51px inset-x-0 h-1px bg-mbBrand/20" />
      <img className="absolute top-0 right-0 w-auto h-full" src={BgDecoTrImg} />
      <img className="absolute bottom-0 left-0 w-auto h-full" src={BgDecoBlImg} />
      <div className="relative w-full h-full pt-68px pb-86px px-28px">
        <LooksRank />
      </div>
    </div>
  );
}
