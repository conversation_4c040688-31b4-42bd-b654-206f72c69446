import type { AggregatePointDetail } from '#app/data/models/aggregatePointDetail';
import { BattleResult } from '@ssglauncher/server-api';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'preact/compat';
import CloseIcon from '../../assets/icon-close_36x36.webp';
import TopBarImg from '../../assets/topbar_651x9.webp';
import BottomBarImg from '../../assets/bottombar_552x1.webp';
import { MissionScoreMatchGroupItem } from '#app/data/components/GameRecord/MissionScoreMatchGroupItem';
import { forceSign } from '#app/data/utils/formatter';
import { DEFAULT_VALUE } from '#app/data/constants/gameRecord';

interface MissionScoreModalProps {
  score?: number | string;
  result?: BattleResult;
  isSingleMode?: boolean;
  data?: AggregatePointDetail;
  visible: boolean;
  onModalClose(): void;
}

function MissionScoreModal({ score, result, isSingleMode, data, visible, onModalClose }: MissionScoreModalProps) {
  const [t] = useTranslation();

  const closedZoneTaskPoint = useMemo(() => {
    const value = data?.closedZoneTaskPoint;
    const name = t('GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.CLOSED_ZONE_TASK_POINT', '封锁区得分');
    const point = forceSign(value?.point, { defaultValue: DEFAULT_VALUE });
    return { name, point };
  }, [data?.closedZoneTaskPoint, t]);

  const taskPoint = useMemo(() => {
    if (result === BattleResult.Victory) {
      return {
        name: t('GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.EVACUATION_SUCCESS', '撤离成功'),
        point: forceSign(data?.taskPoint?.point, { defaultValue: DEFAULT_VALUE }),
      };
    }
    return {
      name: t('GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.MISSION_ABORTED', '任务中止'),
      point: DEFAULT_VALUE,
    };
  }, [result, data?.taskPoint, t]);

  const campDeduct = useMemo(() => {
    const value = data?.campDeduct;
    const name = isSingleMode
      ? t('GAMES:ZONE.GAME_RECORD.KILLED', '被击坠')
      : t('GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.CAMP_DEDUCT', '队员被击坠');
    const point = forceSign(value?.point, { defaultValue: DEFAULT_VALUE });
    const count = value?.count || 0;
    return { name, point, count };
  }, [result, data?.campDeduct, t]);

  const renderIconFields = () => {
    if (!data) return null;
    const items = [];
    for (const [key, item] of Object.entries(data)) {
      if (!item.icon) continue; // 跳过无icon字段
      items.push(<MissionScoreMatchGroupItem key={key} data={item} />);
    }
    return items;
  };

  if (!visible) return null;
  return (
    <div
      className={clsx(
        'w-790px h-fit pt-20px top-[11.4%] right-0 left-0 top-0 bottom-0 m-auto bg-black-100 border-1 border-mbBrand-60 absolute z-10',
      )}
      style={{
        boxShadow: 'box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16)',
      }}
    >
      <div className="absolute top-12px right-12px cursor-pointer z-10" onClick={() => onModalClose()}>
        <img src={CloseIcon} className="w-18px h-18px opacity-60 hover:opacity-80 active:opacity-100" />
      </div>
      <div className="relative w-full h-full flex flex-col">
        <div className="flex justify-center flex-col items-center">
          <div className="mb-4px text-body">{t('GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.TITLE', '任务分')}</div>
          <div className="text-largeTitle font-mecha text-white-90">{score}</div>
        </div>
        <div className="flex w-full items-center justify-center mb-3">
          <img src={TopBarImg} className="w-full h-9px mx-6.5px" />
        </div>
        <div className="grow-1 flex flex-wrap h-278px">{renderIconFields()}</div>
        <div className="flex w-full items-center justify-center">
          <img src={BottomBarImg} className="w-full h-1px mx-24px" />
        </div>
        <div className="shrink-0 flex flex-col gap-y-3 font-mecha px-6 pt-4 pb-6">
          {!isSingleMode && (
            <div className="w-full flex items-center justify-between gap-x-3">
              <span className="text-3/4.5 text-mbSecond-60">{closedZoneTaskPoint.name}</span>
              <span className="text-5/7.5 text-mbBrand-default">{closedZoneTaskPoint.point}</span>
            </div>
          )}
          <div className="w-full flex items-center justify-between gap-x-3">
            <span className="text-3/4.5 text-mbSecond-60">{taskPoint.name}</span>
            <span className="text-5/7.5 text-mbBrand-default">{taskPoint.point}</span>
          </div>
          <div className="w-full flex items-center justify-between gap-x-3">
            <span className="text-3/4.5 text-mbSecond-60">
              {campDeduct.name} x{campDeduct.count}
            </span>
            <span className="text-5/7.5 text-error">{campDeduct.point}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export { MissionScoreModal };
