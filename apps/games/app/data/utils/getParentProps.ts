const getWujieProps = () => {
  return (window as any).$wujie?.props;
};

const getIFrameProps = () => {
  const url = new URL(window.location.href);
  return url.searchParams.get('$props');
};

const getParentProps = (el: HTMLElement) => {
  const wujieProps = getWujieProps();
  if (wujieProps) {
    return wujieProps;
  }
  const iframeProps = getIFrameProps();
  if (iframeProps) {
    return iframeProps;
  }
  return null;
};

export { getParentProps };
