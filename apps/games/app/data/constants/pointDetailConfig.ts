import type { PointDetailConfig } from '#app/data/models/pointDetailConfig';
import { SSG_MB_STATIC_PREFIX } from './gameRecord';

export const POINT_DETAIL_CONFIG: PointDetailConfig = {
  boss: {
    name: '巨型威胁',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.BOSS',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_KillLargeTarget_3DE0FF.png',
  },
  giantInstallation: {
    name: '巨型设施',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.GIANT_INSTALLATION',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_GiantInstallation_3DE0FF.png',
  },
  elite: {
    name: '三代机兵',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.ELITE',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_KillNPC_3DE0FF.png',
  },
  sceneMisc: {
    name: '二代机兵',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.SCENE_MISC',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_KillEnemy_3DE0FF.png',
  },
  defenseInstallation: {
    name: '防御设施',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.DEFENSE_INSTALLATION',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_DefenseInstallation_3DE0FF.png',
  },
  stratumShatterer: {
    name: '地层破碎机',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.STRATUM_SHATTERER',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_KillSubstrate_3DE0FF.png',
  },
  eICNPC: {
    name: 'EIC感染机兵',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.EIC_NPC',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_EICNPC_3DE0FF.png',
  },
  eicJar: {
    name: 'EIC实验体',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.EIC_JAR',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_KillEICJar_3DE0FF.png',
  },
  cargoShip: {
    name: '飞船/战舰',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.REINFORCEMENT',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_KillTransport_3DE0FF.png',
  },
  teamKill: {
    name: '雇佣兵',
    nameIntlKey: 'GAMES:ZONE.GAME_RECORD.DATA_ITEM.RECORD_DETAIL.TEAM_KILL',
    icon: SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/DataIcon/UIDataIcon_CampKill_3DE0FF.png',
  },
  campDeduct: {
    name: '',
    nameIntlKey: '',
    icon: '',
  },
  taskPoint: {
    name: '',
    nameIntlKey: '',
    icon: '',
  },
  closedZoneTaskPoint: {
    name: '',
    nameIntlKey: '',
    icon: '',
  },
};
