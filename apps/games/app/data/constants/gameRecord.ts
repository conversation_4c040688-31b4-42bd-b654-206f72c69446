import { getApiEnv } from '@ssglauncher/utils';
import i18n from '#core/i18n';
import { getAppEnv } from '#common/utils/appEnv';

export const SSG_MB_STATIC_PREFIX = import.meta.env.SSR
  ? ''
  : `${getAppEnv().SSG_GAME_STATIC_PREFIX}/mb/${
      window.__XCEF_ENV__ === 'qa' || getApiEnv() === 'ST' || getApiEnv() === 'PR' ? 'trunk' : 'release'
    }`;

export const DEFAULT_VALUE = '--';

export const DEFAULT_RANK_LEVEL = i18n.t('GAMES:ZONE.GAME_RECORD.DEFAULT_RANK', '青铜Ⅴ');

export const DEFAULT_RANK_ICON = SSG_MB_STATIC_PREFIX + '/Assets/Art/UI/Dynamic/Rank/UIBattleRank_8_5.png';
