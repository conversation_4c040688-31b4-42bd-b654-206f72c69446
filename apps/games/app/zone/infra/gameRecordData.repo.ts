import type { XCefSDK } from '@ssglauncher/sdk';
import type { GameBattleRecordRemote, GamePlayerStatisticV2Remote } from '@ssglauncher/server-api';
import type { SeasonData, GameSeason, GameSeasonOption } from '#domains/zone/model/season';
import type {
  GameRecordQueryParams,
  GameRecordListQueryParams,
  GameRecordCommonParams,
} from '#domains/zone/model/gameRecordQueryParams';
import type { Observable } from 'rxjs';
import {
  BehaviorSubject,
  catchError,
  from,
  map,
  of,
  ReplaySubject,
  shareReplay,
  switchMap,
  tap,
  combineLatest,
  filter,
} from 'rxjs';
import { BSRepository, Repository, InjectPlugin } from '@banshee/ex-banshee';
import { promisify } from '@ssglauncher/sdk';
import { listenerToObserver } from '@ssglauncher/operators';
import { toLocalGameRecord, type MatchRecord } from '#domains/zone/model/gameRecord';
import { XCefSDKSymbol } from '#domains/infra/xcefSdk.plugin';
import { defaultState } from '#app/data/models/gameRecordPageState';
import { toLocalSeasonDataV2 } from '#domains/zone/model/season.v2';

type TXCef = Awaited<ReturnType<typeof XCefSDK>>;

/** 战绩数据是否有变化的回调参数类型，目前该回调函数没有入参 */
type GameRecordChangedCallback = Parameters<TXCef['gameModule']['onGameRecordChanged']>[0];
type GameRecordChangedCallbackParams = Parameters<GameRecordChangedCallback>[0] | undefined;

@Repository('GameRecordDataRepo')
export class GameRecordDataRepo extends BSRepository {
  private xcef$: ReplaySubject<TXCef> = new ReplaySubject(1);
  private isFetching$ = new BehaviorSubject<boolean>(false);
  private isSeasonDataFetching$ = new BehaviorSubject<boolean>(false);
  private isSeasonListFetching$ = new BehaviorSubject<boolean>(false);
  private gameRecordChanged$: BehaviorSubject<GameRecordChangedCallbackParams>;

  constructor(
    @InjectPlugin(XCefSDKSymbol)
    xcefPlugin: typeof XCefSDK,
  ) {
    super();
    this.gameRecordChanged$ = new BehaviorSubject<GameRecordChangedCallbackParams>(undefined);
    this.createGameRecordChangedListener();
    this.init(xcefPlugin);
  }

  isFetching() {
    return this.isFetching$.asObservable();
  }

  isSeasonDataFetching() {
    return this.isSeasonDataFetching$.asObservable();
  }

  toggleSeasonDataFetching() {
    if (!this.isSeasonDataFetching$.getValue()) {
      this.isSeasonDataFetching$.next(true);
      Promise.resolve().then(() => {
        this.isSeasonDataFetching$.next(false);
      });
    }
  }

  isSeasonListFetching() {
    return this.isSeasonListFetching$.asObservable();
  }

  getSeasonList({ server = '' }: GameRecordCommonParams) {
    const paramsStream = of({ server });
    return this.ready(xcef =>
      paramsStream.pipe(
        switchMap<any, Promise<GameSeason[]>>(() => {
          this.isSeasonListFetching$.next(true);
          const asyncFunc = promisify(xcef.gameModuleData.getSeasonList.bind(xcef.gameModuleData));
          return asyncFunc({ server });
        }),
        map(list => {
          return list.map(item => ({
            label: item.name,
            value: item.id,
            shortName: item.shortName,
          }));
        }),
        tap(() => {
          this.isSeasonListFetching$.next(false);
        }),
        catchError(err => {
          console.log(err);
          this.isSeasonListFetching$.next(false);
          return JSON.parse('{}');
        }),
      ),
    ) as Observable<GameSeasonOption[]>;
  }

  getSeasonStatistic({ seasonId, matchMode, server = '' }: GameRecordQueryParams) {
    const paramsStream = of({ seasonId, matchMode });
    const { seasonId: defaultSeasonId } = defaultState;
    return this.ready(xcef =>
      combineLatest([paramsStream, this.gameRecordChanged$]).pipe(
        filter(() => seasonId !== defaultSeasonId),
        switchMap<any, Promise<GamePlayerStatisticV2Remote>>(() => {
          this.isSeasonDataFetching$.next(true);
          const asyncFunc = promisify(xcef.gameModuleData.getSeasonData.bind(xcef.gameModuleData));
          return asyncFunc({ seasonId: Number(seasonId), playType: matchMode, server });
        }),
        map(remote => toLocalSeasonDataV2(remote, matchMode)),
        tap(() => {
          this.isSeasonDataFetching$.next(false);
        }),
        catchError(err => {
          this.isSeasonDataFetching$.next(false);
          console.log(err);
          return JSON.parse('{}');
        }),
      ),
    ) as Observable<SeasonData>;
  }

  getGameRecordList({ seasonId, matchMode, cursor, size, server = '' }: GameRecordListQueryParams) {
    const paramsStream = of({ seasonId, matchMode, cursor, size, server });
    return this.ready(xcef =>
      combineLatest([paramsStream, this.gameRecordChanged$]).pipe(
        filter(([param]) => !!(param.seasonId && param.seasonId !== defaultState.seasonId && param.matchMode)),
        switchMap<[GameRecordListQueryParams, GameRecordChangedCallbackParams], Promise<GameBattleRecordRemote>>(
          ([params]) => {
            this.isFetching$.next(true);
            const { seasonId, matchMode, cursor, size } = params;
            const asyncFunc = promisify(xcef.gameModuleData.getGameRecord.bind(xcef.gameModuleData));
            return asyncFunc({ seasonId: Number(seasonId), playType: matchMode, cursor, size, server });
          },
        ),
        map(record => toLocalGameRecord(record)),
        tap(() => {
          this.isFetching$.next(false);
        }),
        catchError(err => {
          console.log(err);
          return JSON.parse('{}');
        }),
      ),
    ) as Observable<MatchRecord>;
  }

  private ready<T>(observer: (xcef: TXCef) => Observable<T>): Observable<T> {
    return this.xcef$.pipe(
      shareReplay(1),
      switchMap(xcef => observer(xcef)),
    );
  }

  private createGameRecordChangedListener() {
    this.ready(xcef =>
      listenerToObserver<GameRecordChangedCallbackParams>(xcef.gameModule.onGameRecordChanged),
    ).subscribe(this.gameRecordChanged$);
  }

  private init(xcefPlugin: typeof XCefSDK) {
    from(xcefPlugin()).subscribe(this.xcef$);
  }
}
