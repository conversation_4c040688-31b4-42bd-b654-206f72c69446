import { useMemo } from 'react';
import { useService, useStream } from '@banshee/banshee-preact';
import { useRouteQuery } from '#common/hooks/useRouteQuery';
import { ZoneService } from '#domains/zone/service/zone.service';

export function useGameConfigService() {
  const query = useRouteQuery();
  const zoneService = useService(ZoneService);
  const appCode = query?.code;

  const gameConfig = useStream(props => zoneService?.queryGameConfig(props?.[0]), undefined, [appCode]);
  const isFetchingGameConfig = useStream(() => zoneService.isFetchingGameConfig());
  const hasGameConfigEverSucceeded = useStream(() => zoneService.hasGameConfigEverSucceeded(), false);

  const machineConfig = gameConfig?.supportConfigs || [];

  const config = useMemo(
    () =>
      machineConfig.map(item => ({
        label: item.name,
        content: item.info,
      })) || [],
    [machineConfig],
  );

  return {
    title: gameConfig?.supportConfigName,
    config,
    isFetchingGameConfig,
    hasGameConfigEverSucceeded,
  };
}
