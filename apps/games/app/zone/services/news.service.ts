import type { BaseEntity } from '#domains/zone/model/base.entity';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useEvent, useService, useStream } from '@banshee/banshee-preact';
import { useSearchParams } from '@ssglauncher/hooks';

import { ZoneService } from '#domains/zone/service/zone.service';
import { InformationCategoryChangedEvent } from '#domains/zone/event/informationCategoryChanged.event';

export function useNewsService() {
  const query = useSearchParams();
  const zoneService = useService(ZoneService);
  const event = useEvent();

  const [currentTab, setTab] = useState('');
  const appCode = query?.code;

  const news = useStream(props => zoneService.queryNewsField(props?.[0]), [], [appCode]);
  const banners = useStream(props => zoneService.queryNewsBanner(props?.[0]), [], [appCode]);
  const isFetchingNews = useStream(() => zoneService.isFetchingNews(), true);
  const hasNewsEverSucceeded = useStream(() => zoneService.hasNewsEverSucceeded(), false);
  const notify = useStream(props => zoneService.queryNotification(props?.[0]), undefined, [appCode]);
  const banner = useMemo(() => {
    return banners[0]?.banners.length !== 0 ? banners[0] : null;
  }, [banners]);

  useEffect(() => {
    setTab((banner || news[0])?.title || '');
  }, [news, banner]);

  const handleSelect = (val: string) => {
    event.emit(new InformationCategoryChangedEvent(appCode!, val));

    setTab(val);
  };

  const handleClick = useCallback(
    (item: BaseEntity) => () => {
      zoneService?.cmdClicked(item);
    },
    [zoneService],
  );

  return {
    currentTab,
    notify,
    news,
    banner,
    isFetchingNews,
    hasNewsEverSucceeded,

    handleSelect,
    handleClick,
  };
}
