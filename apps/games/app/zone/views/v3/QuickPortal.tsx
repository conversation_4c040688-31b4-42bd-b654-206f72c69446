import { GAMEPAD_NAV_GROUP, GAMEPAD_NAV_INDEX, navAttrs } from '@ssglauncher/gamepad';
import { useState } from 'preact/compat';
import QrCodeButton from '../../components/QrcodeNew/QrCodeButton';
import { useQuickPortalService } from '../../services/quickPortal.service';

function QuickPortal() {
  const quickPortalService = useQuickPortalService();
  const [opening, setOpening] = useState('');

  if (quickPortalService.isFetchingShortcuts && !quickPortalService.hasShortcutsEverSucceeded) {
    return (
      <div className="flex flex-col justify-between gap-y-sp24">
        <div className="w-sp40 h-sp40 bg-white-4" />
        <div className="w-sp40 h-sp40 bg-white-4" />
        <div className="w-sp40 h-sp40 bg-white-4" />
        <div className="w-sp40 h-sp40 bg-white-4" />
      </div>
    );
  }

  return quickPortalService.contacts.length ? (
    <div className="flex flex-col gap-y-sp24">
      {quickPortalService.contacts.map((item, index, { length }) => (
        <QrCodeButton.Control
          attrs={navAttrs(navAttrs.index(GAMEPAD_NAV_GROUP.GAMES_ZONE_EXTENSION, index), 'y', {
            up: index === 0 ? navAttrs.index(GAMEPAD_NAV_GROUP.MAIN_MENU_BAR, GAMEPAD_NAV_INDEX.WCB_START) : undefined,
            down: index === length - 1 ? GAMEPAD_NAV_GROUP.MAIN_BOTTOM_BAR : undefined,
            left: 'none',
            right: 'none',
          })}
          key={item.id}
          icon={item.icon}
          qrcodes={item.details}
          onClick={quickPortalService.handleClick(item)}
          onHover={quickPortalService.handleHover(item)}
          open={opening === item.id}
          onOpenChange={open => (open ? setOpening(item.id) : setOpening(''))}
        />
      ))}
    </div>
  ) : null;
}

export default QuickPortal;
