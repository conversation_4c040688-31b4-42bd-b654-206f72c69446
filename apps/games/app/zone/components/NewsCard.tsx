import { NewsEntity } from '#domains/zone/model/news.entity';

type NewsCardProps = Omit<NewsEntity, 'srcType' | 'srcValue' | 'id' | 'gameCode' | 'navigate'>;

function NewsCard({ title, content, desc }: NewsCardProps) {
  return (
    <a target="_blank" className="cursor-pointer">
      <article className="p-sp20 pb-sp16 border rounded-small backdrop-blur-frosted bg-white-4  border-white-4 hover:bg-white-8 hover:border-white-8">
        <h2 title={title} className="color-white-90 text-size-title font-bold mb-sp12 line-clamp-1">
          {title}
        </h2>
        <div
          title={content}
          className="color-white-40 text-size-body line-clamp-2 min-h-[36px]"
          dangerouslySetInnerHTML={{ __html: content }}
        />
        <div className="color-white-40 text-size-body mt-sp12">{desc}</div>
      </article>
    </a>
  );
}

export default NewsCard;
