import { EVENT_SUB_APP_ACTIVATED, EVENT_SUB_APP_DEACTIVATED, type MessageEventData } from '@ssglauncher/shared/events';
import { useState, useEffect } from 'react';

export function useWindowActivated() {
  const [windowActivated, setWindowActivated] = useState(true);
  useEffect(() => {
    const handler = (evt: MessageEvent<MessageEventData>) => {
      const eType = evt.data?.type;
      if (eType === EVENT_SUB_APP_ACTIVATED) {
        setWindowActivated(true);
      } else if (eType === EVENT_SUB_APP_DEACTIVATED) {
        setWindowActivated(false);
      }
    };

    window.addEventListener('message', handler);
    return () => window.removeEventListener('message', handler);
  }, []);

  return windowActivated;
}
