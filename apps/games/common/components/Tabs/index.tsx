import type { VNode } from 'preact';
import { Children, cloneElement, PropsWithChildren, useCallback, useMemo, useState } from 'react';
import Tab from './Tab';
import TabPanel, { TabPanelProps } from './TabPanel';

interface TabsProps {
  value: string;
  onChange?(val: string): void;
  onSelect?(val: string): void;
}

function Tabs({ value, onChange, onSelect, children }: PropsWithChildren<TabsProps>) {
  const [tabs, setTabs] = useState<TabPanelProps[]>([]);

  const getChildren = useMemo(() => {
    const panes = Children.toArray(children);
    const tabs = [];
    const components = [];

    for (const child of panes) {
      const com = child.valueOf() as { props: TabPanelProps };
      tabs.push(com.props);
      setTabs(tabs);

      components.push(
        cloneElement(child as VNode, {
          ...com.props,
          isShow: value === com.props.index,
        }),
      );
    }

    return components;
  }, [children, value]);

  const handleClick = useCallback(
    (index: string) => () => {
      onSelect?.(index);
      onChange?.(index);
    },
    [onChange, onSelect],
  );

  return (
    <div className="w-full">
      <ul className="flex py-sp12 border-b border-b-white-4 px-sp20">
        {tabs.map(item => (
          <Tab key={item.index} title={item.title} isActive={item.index === value} onClick={handleClick(item.index)} />
        ))}
      </ul>
      <div className="py-sp16 px-sp20 text-size-body">{getChildren}</div>
    </div>
  );
}

Tabs.TabPanel = TabPanel;
export default Tabs;
