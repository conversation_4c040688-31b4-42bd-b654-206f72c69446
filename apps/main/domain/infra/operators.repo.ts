import type { XCefSDK } from '@ssglauncher/sdk';

export abstract class IOperatorsRepo {
  abstract openWebDialog(
    options: Omit<
      Parameters<Awaited<ReturnType<typeof XCefSDK>>['uiModule']['openWebDialog']>[0],
      'onSuccess' | 'onError'
    >,
  ): void;
  abstract fetchRemoteConfigData(): Promise<Record<string, any>>;
  abstract switchGame(gameId: string): void;
  abstract report(
    options: Omit<
      Parameters<Awaited<ReturnType<typeof XCefSDK>>['dataAnalysisModule']['report']>[0],
      'onSuccess' | 'onError'
    >,
  ): void;
  abstract openConfirmDialog(
    options: Omit<
      Parameters<Awaited<ReturnType<typeof XCefSDK>>['uiModule']['openConfirmDialog']>[0],
      'onSuccess' | 'onError' | 'url' | 'withCloseButton'
    >,
  ): Promise<[boolean, boolean]>;
}
