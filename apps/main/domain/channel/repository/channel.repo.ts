import type { Observable } from 'rxjs';

export interface ChannelConfig {
  AboutUsUrl: string;
  ConnectServiceUrl: string;
  HealthAdvice: string;
  Channel: string;
  Modules: {
    /** 允许启动器账号模块 */
    EnableAccountModule: boolean;
    /** 开启账号模块-账号管理 @since v2.6.0 */
    EnableAccountModuleAccountManage?: boolean;
    /** 开启账号模块-设备管理 @since v2.6.0 */
    EnableAccountModuleDeviceManage?: boolean;
    /** 允许用户进入游戏首页 */
    EnableGameHomePage: boolean;
    /** 展示启动器左侧游戏导航栏 */
    EnableGameLeftBar: boolean;
    /** 允许切换启动器语言 */
    EnableLauncherChangeLanguage: boolean;
    /** 展示启动器菜单快捷入口 */
    EnableLauncherShowMenuEnter: boolean;
    /** 展示启动器开屏loading图 */
    EnableLauncherShowSplash: boolean;
    /** 提审合规健康忠告 */
    EnableMakeHealthAdvice: boolean;
    /** 提审合规全局英文替换为中文 */
    EnableReplaceEnToCn: boolean;
  };
}

export abstract class IChannelRepo {
  abstract getConfig(): Observable<ChannelConfig>;
}
