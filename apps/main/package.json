{"name": "ssglauncher-app-main", "version": "1.6.0", "private": true, "type": "module", "scripts": {"build": "vite build", "build:prod": "vite build --mode prod", "dev": "vite --host --force", "prepare": "pnpm dlx sort-package-json@2.15.1"}, "dependencies": {"@arms/rum-browser": "~0.0.34", "@arms/rum-vite-plugin": "~0.0.16", "@banshee/banshee-preact": "^1.4.5", "@banshee/ex-banshee": "^0.6.3", "@loadable/component": "^5.15.3", "@preact/signals": "~2.0.1", "@ssglauncher/biz-core": "workspace:*", "@ssglauncher/components": "workspace:*", "@ssglauncher/gamepad": "workspace:*", "@ssglauncher/hooks": "workspace:*", "@ssglauncher/iconfont": "workspace:*", "@ssglauncher/operators": "workspace:*", "@ssglauncher/sdk": "^2.3.29", "@ssglauncher/server-api": "workspace:*", "@ssglauncher/shared": "workspace:*", "@ssglauncher/tracking": "workspace:*", "@ssglauncher/utils": "workspace:*", "ahooks": "^3.8.4", "clsx": "^2.0.0", "i18next": "^23.7.8", "jotai": "~2.8.0", "preact": "10.19.3", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "rxjs": "^7.8.1", "wujie": "^1.0.21"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.23.5", "@babel/plugin-transform-class-properties": "^7.23.3", "@preact/preset-vite": "^2.7.0", "@ssglauncher/shared": "workspace:*", "@ssglauncher/theme": "workspace:*", "@types/loadable__component": "^5.13.8", "@types/lodash-es": "~4.17.12", "@types/node": "^20.8.9", "@types/react": "^18.2.43", "@unocss/reset": "^0.58.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "unocss": "^0.58.0", "unocss-preset-scrollbar-hide": "^1.0.1", "vite": "^5.0.7"}}