import { Suspense, memo } from 'react';
import { I18nextProvider } from 'react-i18next';
import { Outlet } from 'react-router-dom';
import { BansheeProvider } from '@banshee/banshee-preact';
import { useGamepadNavigation } from '@ssglauncher/gamepad';
import i18n from '#common/util/i18n';
import { ApplicationLayout } from '#common/component/ApplicationLayout';
import { GameBar } from '#app/gameManager/view/GameBar';
import { Header } from '#app/window/view/Header';
import { createBanshee } from '#common/util/container';
import { IFrameDialog } from '#common/component/IFrameDialog';

const banshee = createBanshee();

{
  // 保存上一次记录的时间戳
  let previousTime: number | null = null;

  // 定义 console.pin 类型，以便扩展 Console 接口
  interface Console {
    pin: (label?: string) => void;
  }

  // 时间格式化辅助函数：将 Date 对象格式化为 "HH:MM:SS.ms" 形式
  function formatTimeToReadable(date: Date): string {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
    return `${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  // 实现 console.pin 方法
  (console as any).pin = function (label = ''): void {
    const now = new Date();
    const currentTime = now.getTime();
    const readableTime = formatTimeToReadable(now);
    let message: string;

    if (previousTime === null) {
      // 第一次记录，没有时间差
      message = `>>> ${readableTime} ${label ? label + ' ' : ''}(start)`;
    } else {
      // 计算与上一次记录的时间差
      const diff = currentTime - previousTime;
      message = `>>> ${readableTime} ${label ? label + ' ' : ''}(+${diff}ms)`;
    }

    previousTime = currentTime; // 更新上一次记录的时间
    console.log(message); // 输出日志
  };
}

function App() {
  if (window.__XCEF_LANG__) {
    i18n.changeLanguage(window.__XCEF_LANG__);
  }

  (console as any).pin('app start');

  useGamepadNavigation('main');

  (window as any).openDialog = IFrameDialog.openDialog;

  return (
    <Suspense fallback={null}>
      <I18nextProvider i18n={i18n}>
        <BansheeProvider banshee={banshee}>
          <ApplicationLayout header={<Header />} aside={<GameBar />}>
            <Outlet />
          </ApplicationLayout>
        </BansheeProvider>
      </I18nextProvider>
    </Suspense>
  );
}

export default memo(App);
