import type { HTMLAttributes, RefObject } from 'preact/compat';
import type { startOptions } from 'wujie';
import { PureComponent, createRef } from 'preact/compat';
import { bus, destroyApp, preloadApp, setupApp, startApp } from 'wujie';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../../component/ConnectFailed';
import windowResizePlugin from '#domains/infra/windowResize.plugin';
import { logger } from '#common/util/logger';

// 其背后实现都是 lifecycle，随便选一个都一样
type TLifeCycle = startOptions['activated'];

interface SubAppProps extends HTMLAttributes<HTMLDivElement> {
  app: Omit<startOptions, 'el'>;
  reload?: () => void;
  onActivated?: TLifeCycle;
  onDeactivated?: TLifeCycle;
}

class SubAppWuJie extends PureComponent<SubAppProps, { ref: RefObject<HTMLDivElement>; isError: boolean }> {
  // readonly setupApp = setupApp;
  // readonly preloadApp = preloadApp;
  // readonly destroyApp = destroyApp;
  // readonly bus = bus;
  static bus = bus;
  static setupApp = setupApp;
  static preloadApp = preloadApp;
  static destroyApp = destroyApp;

  // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
  private destroy!: Function | void;
  private startAppQueue = Promise.resolve();

  constructor(props: SubAppProps) {
    super(props);

    this.start = this.start.bind(this);
    this.reload = this.reload.bind(this);
    this.loadError = this.loadError.bind(this);

    this.state = {
      isError: false,
      ref: createRef(),
    };
  }

  componentDidMount() {
    this.start();
  }

  // componentWillUnmount() {
  //   console.log('inner destroy');
  //   if (this.destroy) {
  //     this.destroy.apply(this);
  //   }
  // }

  componentDidUpdate(prevProps: Readonly<SubAppProps>) {
    if (this.props.app.name !== prevProps.app.name || this.props.app.url !== prevProps.app.url) {
      // eslint-disable-next-line react/no-did-update-set-state
      this.setState({ isError: false });
      this.start();
    }
  }

  reload() {
    // this.props.reload?.();
    // window.location.reload();
    // new AboutLauncherTracking((p: any) => (window as any).xcef.dataAnalysisModule.report(p)).ssgReloadEvent({
    //   reloadSource: this.props.app.url,
    // });
  }

  loadError(url: string, e: Error) {
    console.error('页面连接失败，错误：%s，连接地址：%s', e, url);
    this.setState({ isError: true });
  }

  render() {
    return (
      <div ref={this.props.ref} className="h-full">
        {!this.state.isError ? (
          <div ref={this.state.ref} className={this.props.className} style={this.props.style} />
        ) : (
          <ConnectFailed onClick={this.reload} />
        )}
      </div>
    );
  }

  private async start() {
    const loading = document.createElement('div');
    const img = new Image();
    img.src = (await import('../../assets/Loading.webp')).default;
    loading.appendChild(img);
    loading.classList.add('flex', 'items-center', 'justify-center', '-mt-0px');
    logger('[SubApp]', { url: this.props.app.url });

    try {
      let config: startOptions = {
        ...this.props.app,
        el: this.state.ref.current,
        activated: this.props.onActivated,
        deactivated: this.props.onDeactivated,
        plugins: [windowResizePlugin],
        // degrade: !this.props.app.url.includes('launcher-static'),
        // degrade: true,
        loading,
        loadError: this.loadError,
        afterMount: () => {
          console.log('inner load finish');
        },
        afterUnmount: () => {
          console.log('inner destroy finish');
        },
        // 由于`xcef.networkModule.request`请求比fetch慢而导致wujie请求异步脚本中断报错的概率提高（脚本中断报错由于框架选型的原因无法根本解决）
        // 并且，fetch no-cors模式会导致无法获取response body
        // 同时，目前production和development模式下都是同源的简单请求，不需要区分处理
        // 因此不再设置自定义fetch方法，使用默认的`window.fetch`即可
        // fetch: (url, options) => {
        //   const _url = url.toString();
        //   return _url.includes('launcher-static')
        //     ? window.fetch(url, {
        //         ...options,
        //         mode: 'no-cors',
        //       })
        //     : new Promise((resolve, reject) => {
        //         window.xcef.networkModule.request({
        //           url: _url.startsWith('//') ? `${window.location.protocol}${_url}` : _url,
        //           method: 'get',
        //           parseAsJson: false,
        //           onSuccess: res => {
        //             resolve(new Response(res));
        //           },
        //           onError: reject,
        //         });
        //       });
        // },
      };
      console.log({config})
      this.destroy = await startApp(config);
    } catch (error) {
      console.error('inner fetch 异常：%s', error);
      this.setState({ isError: true });
    }
  }
}

export default SubAppWuJie;
