import { useMainViewerService } from '#app/subappManager/service/mainViewer.service';
import { SubAppLoading } from '#app/subappManager/component/SubAppLoading';
import { WuJieViewer } from '#app/subappManager/view/WuJieViewer/WuJieViewer';
import IFrameViewer from '#app/subappManager/view/IFrameViewer/IFrameViewer';

const MainViewerManager = () => {
  const service = useMainViewerService();

  const renderContent = () => {
    const url = service.subAppConfig.url;
    const isReady = service.pageShow && url;

    if (!isReady) {
      return <SubAppLoading />;
    }

    /**
     * 判断 url query 是否包含 wujie 参数
     */
    let hasWujieQuery = (() => {
      let hasWujieQuery = false;
      const uri = new URL(url);
      uri.searchParams.forEach((value, key) => {
        if (key === 'wujie') {
          hasWujieQuery = true;
        }
      });
      return hasWujieQuery;
    })();

    if (hasWujieQuery) {
      return <WuJieViewer service={service} />;
    }
    return <IFrameViewer service={service} />;
  };

  return renderContent();
};

export default MainViewerManager;
