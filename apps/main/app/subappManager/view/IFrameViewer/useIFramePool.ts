import { useRef, useEffect, useCallback, useState } from 'preact/hooks';
import { IFramePool, globalIFramePool, type IFramePoolConfig } from './IFramePool';

export interface UseIFramePoolOptions extends IFramePoolConfig {
  /** 是否使用全局池，默认 true */
  useGlobalPool?: boolean;
  /** 预加载的 URL 列表 */
  preloadUrls?: string[];
  /** 是否启用预加载，默认 true */
  enablePreload?: boolean;
}

export interface UseIFramePoolReturn {
  /** iframe 池实例 */
  pool: IFramePool;
  /** 获取或创建 iframe */
  getIFrame: (src: string, container?: HTMLElement) => Promise<HTMLIFrameElement>;
  /** 预加载 iframe */
  preloadIFrame: (src: string) => Promise<boolean>;
  /** 批量预加载 iframe */
  preloadBatch: (urls: string[]) => Promise<number>;
  /** 释放 iframe */
  releaseIFrame: (src: string) => void;
  /** 移除 iframe */
  removeIFrame: (src: string) => void;
  /** 清理空闲的 iframe */
  cleanup: () => void;
  /** 清空整个池子 */
  clear: () => void;
  /** 获取池子状态 */
  getStatus: () => ReturnType<IFramePool['getStatus']>;
  /** 池子状态（实时更新） */
  status: ReturnType<IFramePool['getStatus']>;
}

/**
 * IFrame Pool Hook
 * 提供简化的 iframe pool 使用接口
 * 
 * @param options - 配置选项
 * @returns iframe pool 相关方法和状态
 * 
 * @example
 * ```tsx
 * // 使用全局池
 * const { pool, getIFrame, status } = useIFramePool({
 *   preloadUrls: ['https://example1.com', 'https://example2.com']
 * });
 * 
 * // 使用自定义池
 * const { pool, getIFrame } = useIFramePool({
 *   useGlobalPool: false,
 *   maxSize: 5,
 *   maxIdleTime: 2 * 60 * 1000 // 2分钟
 * });
 * 
 * // 在组件中使用
 * const iframe = await getIFrame('https://example.com', containerRef.current);
 * ```
 */
export const useIFramePool = (options: UseIFramePoolOptions = {}): UseIFramePoolReturn => {
  const {
    useGlobalPool = true,
    preloadUrls = [],
    enablePreload = true,
    ...poolConfig
  } = options;

  // 创建或使用池实例
  const poolRef = useRef<IFramePool | null>(null);
  const [status, setStatus] = useState(() => 
    useGlobalPool ? globalIFramePool.getStatus() : { total: 0, inUse: 0, loaded: 0, loading: 0, error: 0, preloading: 0, maxSize: 0 }
  );

  // 初始化池子
  useEffect(() => {
    if (useGlobalPool) {
      poolRef.current = globalIFramePool;
    } else {
      poolRef.current = new IFramePool(poolConfig);
    }

    // 初始状态
    setStatus(poolRef.current.getStatus());
  }, [useGlobalPool, poolConfig]);

  // 定期更新状态
  useEffect(() => {
    const interval = setInterval(() => {
      if (poolRef.current) {
        setStatus(poolRef.current.getStatus());
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // 预加载 URLs
  useEffect(() => {
    if (enablePreload && preloadUrls.length > 0 && poolRef.current) {
      poolRef.current.preloadBatch(preloadUrls).then(() => {
        if (poolRef.current) {
          setStatus(poolRef.current.getStatus());
        }
      });
    }
  }, [preloadUrls, enablePreload]);

  // 组件卸载时清理（仅限非全局池）
  useEffect(() => {
    return () => {
      if (!useGlobalPool && poolRef.current) {
        poolRef.current.destroy();
        poolRef.current = null;
      }
    };
  }, [useGlobalPool]);

  // 包装方法以更新状态
  const wrapMethod = useCallback(<T extends any[], R>(
    method: (...args: T) => R
  ) => {
    return (...args: T): R => {
      const result = method(...args);
      
      // 异步更新状态
      setTimeout(() => {
        if (poolRef.current) {
          setStatus(poolRef.current.getStatus());
        }
      }, 0);
      
      return result;
    };
  }, []);

  const wrapAsyncMethod = useCallback(<T extends any[], R>(
    method: (...args: T) => Promise<R>
  ) => {
    return async (...args: T): Promise<R> => {
      const result = await method(...args);
      
      // 更新状态
      if (poolRef.current) {
        setStatus(poolRef.current.getStatus());
      }
      
      return result;
    };
  }, []);

  // 获取或创建 iframe
  const getIFrame = useCallback(async (src: string, container?: HTMLElement) => {
    if (!poolRef.current) {
      throw new Error('IFrame pool not initialized');
    }
    
    const result = await poolRef.current.getIFrame(src, container);
    setStatus(poolRef.current.getStatus());
    return result;
  }, []);

  // 预加载 iframe
  const preloadIFrame = useCallback(async (src: string) => {
    if (!poolRef.current) {
      throw new Error('IFrame pool not initialized');
    }
    
    const result = await poolRef.current.preloadIFrame(src);
    setStatus(poolRef.current.getStatus());
    return result;
  }, []);

  // 批量预加载
  const preloadBatch = useCallback(async (urls: string[]) => {
    if (!poolRef.current) {
      throw new Error('IFrame pool not initialized');
    }
    
    const result = await poolRef.current.preloadBatch(urls);
    setStatus(poolRef.current.getStatus());
    return result;
  }, []);

  // 释放 iframe
  const releaseIFrame = useCallback((src: string) => {
    if (!poolRef.current) return;
    
    poolRef.current.releaseIFrame(src);
    setStatus(poolRef.current.getStatus());
  }, []);

  // 移除 iframe
  const removeIFrame = useCallback((src: string) => {
    if (!poolRef.current) return;
    
    poolRef.current.removeIFrame(src);
    setStatus(poolRef.current.getStatus());
  }, []);

  // 清理空闲的 iframe
  const cleanup = useCallback(() => {
    if (!poolRef.current) return;
    
    poolRef.current.cleanup();
    setStatus(poolRef.current.getStatus());
  }, []);

  // 清空整个池子
  const clear = useCallback(() => {
    if (!poolRef.current) return;
    
    poolRef.current.clear();
    setStatus(poolRef.current.getStatus());
  }, []);

  // 获取池子状态
  const getStatus = useCallback(() => {
    if (!poolRef.current) {
      return { total: 0, inUse: 0, loaded: 0, loading: 0, error: 0, preloading: 0, maxSize: 0 };
    }
    
    return poolRef.current.getStatus();
  }, []);

  return {
    pool: poolRef.current!,
    getIFrame,
    preloadIFrame,
    preloadBatch,
    releaseIFrame,
    removeIFrame,
    cleanup,
    clear,
    getStatus,
    status
  };
};

/**
 * 简化版 Hook，仅用于获取 iframe
 * 
 * @param src - iframe 源地址
 * @param container - 容器元素引用
 * @returns iframe 元素和加载状态
 * 
 * @example
 * ```tsx
 * const containerRef = useRef<HTMLDivElement>(null);
 * const { iframe, loading, error } = useIFrame('https://example.com', containerRef);
 * ```
 */
export const useIFrame = (src: string, container: React.RefObject<HTMLElement>) => {
  const { getIFrame, releaseIFrame } = useIFramePool();
  const [iframe, setIFrame] = useState<HTMLIFrameElement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!src || !container.current) return;

    let mounted = true;

    const loadIFrame = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const iframeElement = await getIFrame(src, container.current!);
        
        if (mounted) {
          setIFrame(iframeElement);
          setLoading(false);
        }
      } catch (err) {
        if (mounted) {
          setError(err as Error);
          setLoading(false);
        }
      }
    };

    loadIFrame();

    return () => {
      mounted = false;
      if (iframe) {
        releaseIFrame(src);
      }
    };
  }, [src, container, getIFrame, releaseIFrame]);

  return { iframe, loading, error };
};
