import { useRef, useState, useEffect, useCallback, useMemo } from 'preact/hooks';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../../component/ConnectFailed';
import { logger } from '#common/util/logger';
import { SubAppLoading } from '#app/subappManager/component/SubAppLoading';
import { globalIFramePool, type IFramePool } from './IFramePool';
import type { useMainViewerService } from '#app/subappManager/service/mainViewer.service';

export interface PooledIFrameViewerProps {
  /** 主查看器服务实例 */
  service: ReturnType<typeof useMainViewerService>;
  /** 自定义 iframe 池实例，默认使用全局池 */
  pool?: IFramePool;
  /** 预加载的 URL 列表 */
  preloadUrls?: string[];
  /** 是否启用预加载，默认 true */
  enablePreload?: boolean;
}

/**
 * 基于池化的 IFrame 查看器组件
 * 使用 iframe pool 来提高页面切换性能
 * 
 * 功能特性:
 * - iframe 复用和池化管理
 * - 预加载支持
 * - 快速 tab 切换
 * - 自动资源清理
 * - 错误处理和重试
 */
export const PooledIFrameViewer = ({
  service,
  pool = globalIFramePool,
  preloadUrls = [],
  enablePreload = true
}: PooledIFrameViewerProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const currentIFrameRef = useRef<HTMLIFrameElement | null>(null);
  const isMountedRef = useRef(true);

  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // 生成带 props 的 URL
  const iframeSrc = useMemo(() => {
    const { props: appProps, url } = service.subAppConfig;
    if (!appProps) return url;
    
    const uri = new URL(url);
    uri.searchParams.set('$props', JSON.stringify(appProps));
    return uri.toString();
  }, [service.subAppConfig.url, service.subAppConfig.props]);

  /**
   * 设置错误状态
   */
  const setErrorState = useCallback((error: Error) => {
    if (!isMountedRef.current) return;
    
    console.error('PooledIFrame 加载失败:', error.message, 'URL:', iframeSrc);
    logger('[PooledIFrameViewer] Error', { url: iframeSrc, error: error.message });
    
    setIsError(true);
    setIsLoading(false);
    setErrorMessage(error.message);
  }, [iframeSrc]);

  /**
   * 加载 iframe
   */
  const loadIFrame = useCallback(async () => {
    if (!containerRef.current || !iframeSrc) return;

    try {
      setIsLoading(true);
      setIsError(false);
      setErrorMessage('');

      // 释放之前的 iframe
      if (currentIFrameRef.current) {
        const oldSrc = currentIFrameRef.current.src;
        if (oldSrc && oldSrc !== iframeSrc) {
          pool.releaseIFrame(oldSrc);
        }
      }

      console.log(`[PooledIFrameViewer] 开始加载: ${iframeSrc}`);
      
      // 从池子获取 iframe
      const iframe = await pool.getIFrame(iframeSrc, containerRef.current);
      
      if (!isMountedRef.current) return;

      // 显示 iframe
      iframe.style.display = 'block';
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      
      currentIFrameRef.current = iframe;
      setIsLoading(false);
      
      console.log(`[PooledIFrameViewer] 加载成功: ${iframeSrc}`);
      
      // 调用激活回调
      service.handleActivated?.(null);
      
    } catch (error) {
      setErrorState(error as Error);
    }
  }, [iframeSrc, pool, service.handleActivated, setErrorState]);

  /**
   * 重新加载
   */
  const reload = useCallback(() => {
    if (!isMountedRef.current) return;
    
    // 从池子中移除当前 iframe，强制重新创建
    if (currentIFrameRef.current) {
      pool.removeIFrame(iframeSrc);
      currentIFrameRef.current = null;
    }
    
    // 重新加载
    loadIFrame();
    
    // 上报重载事件
    new AboutLauncherTracking((p: any) => (window as any).xcef?.dataAnalysisModule?.report(p))
      .ssgReloadEvent({ reloadSource: iframeSrc });
  }, [iframeSrc, pool, loadIFrame]);

  /**
   * 预加载 URLs
   */
  const preloadUrls_internal = useCallback(async (urls: string[]) => {
    if (!enablePreload || urls.length === 0) return;
    
    console.log(`[PooledIFrameViewer] 开始预加载 ${urls.length} 个 URL`);
    const successCount = await pool.preloadBatch(urls);
    console.log(`[PooledIFrameViewer] 预加载完成: ${successCount}/${urls.length}`);
  }, [pool, enablePreload]);

  // URL 变化时重新加载
  useEffect(() => {
    if (iframeSrc) {
      loadIFrame();
    }
  }, [iframeSrc, loadIFrame]);

  // 预加载 URLs
  useEffect(() => {
    if (preloadUrls.length > 0) {
      preloadUrls_internal(preloadUrls);
    }
  }, [preloadUrls, preloadUrls_internal]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      
      // 释放当前 iframe
      if (currentIFrameRef.current && iframeSrc) {
        pool.releaseIFrame(iframeSrc);
      }
      
      logger('[PooledIFrameViewer] 组件卸载');
    };
  }, [iframeSrc, pool]);

  /**
   * 渲染加载状态
   */
  const renderLoading = () => {
    if (!isLoading) return null;
    
    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black-100/80 z-10">
        <div className="flex flex-col items-center gap-4">
          <SubAppLoading />
          <p className="text-white/60 text-3.5/5">正在加载内容...</p>
        </div>
      </div>
    );
  };

  /**
   * 渲染错误状态
   */
  const renderError = () => {
    if (!isError) return null;
    
    return (
      <div className="absolute inset-0 flex items-center justify-center">
        <ConnectFailed 
          onClick={reload}
          message={errorMessage || '加载失败，请重试'}
        />
      </div>
    );
  };

  return (
    <div className="w-full h-full flex-1 relative">
      {/* iframe 容器 */}
      <div 
        ref={containerRef} 
        className="w-full h-full"
      />
      
      {/* 加载状态 */}
      {renderLoading()}
      
      {/* 错误状态 */}
      {renderError()}
    </div>
  );
};

export default PooledIFrameViewer;
