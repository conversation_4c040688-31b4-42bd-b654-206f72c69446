/**
 * IFrame 池管理器
 * 用于管理和复用 iframe 实例，提高页面切换性能
 */

export interface IFramePoolItem {
  /** iframe 元素 */
  iframe: HTMLIFrameElement;
  /** 源地址 */
  src: string;
  /** 是否正在使用 */
  inUse: boolean;
  /** 是否已加载完成 */
  loaded: boolean;
  /** 创建时间 */
  createdAt: number;
  /** 最后使用时间 */
  lastUsedAt: number;
  /** 加载状态 */
  loadState: 'loading' | 'loaded' | 'error';
  /** 错误信息 */
  error?: Error;
}

export interface IFramePoolConfig {
  /** 池子最大容量，默认 10 */
  maxSize?: number;
  /** iframe 最大空闲时间（毫秒），默认 5 分钟 */
  maxIdleTime?: number;
  /** 预加载超时时间（毫秒），默认 30 秒 */
  preloadTimeout?: number;
  /** 是否启用自动清理，默认 true */
  autoCleanup?: boolean;
  /** 清理检查间隔（毫秒），默认 1 分钟 */
  cleanupInterval?: number;
}

/**
 * IFrame 池管理器类
 * 提供 iframe 的创建、复用、预加载和清理功能
 */
export class IFramePool {
  private pool: Map<string, IFramePoolItem> = new Map();
  private config: Required<IFramePoolConfig>;
  private cleanupTimer?: NodeJS.Timeout;
  private preloadQueue: Set<string> = new Set();

  constructor(config: IFramePoolConfig = {}) {
    this.config = {
      maxSize: config.maxSize ?? 10,
      maxIdleTime: config.maxIdleTime ?? 5 * 60 * 1000, // 5分钟
      preloadTimeout: config.preloadTimeout ?? 30 * 1000, // 30秒
      autoCleanup: config.autoCleanup ?? true,
      cleanupInterval: config.cleanupInterval ?? 60 * 1000, // 1分钟
    };

    if (this.config.autoCleanup) {
      this.startAutoCleanup();
    }
  }

  /**
   * 获取或创建 iframe
   * @param src - iframe 源地址
   * @param container - 容器元素
   * @returns iframe 元素
   */
  async getIFrame(src: string, container?: HTMLElement): Promise<HTMLIFrameElement> {
    const existing = this.pool.get(src);
    
    if (existing && existing.loadState === 'loaded') {
      // 复用已加载的 iframe
      existing.inUse = true;
      existing.lastUsedAt = Date.now();
      
      if (container && existing.iframe.parentNode !== container) {
        container.appendChild(existing.iframe);
      }
      
      console.log(`[IFramePool] 复用 iframe: ${src}`);
      return existing.iframe;
    }

    // 创建新的 iframe
    return this.createIFrame(src, container);
  }

  /**
   * 预加载 iframe
   * @param src - iframe 源地址
   * @returns Promise<boolean> 是否预加载成功
   */
  async preloadIFrame(src: string): Promise<boolean> {
    if (this.pool.has(src) || this.preloadQueue.has(src)) {
      return true; // 已存在或正在预加载
    }

    if (this.pool.size >= this.config.maxSize) {
      this.cleanup(); // 清理空闲的 iframe
      
      if (this.pool.size >= this.config.maxSize) {
        console.warn(`[IFramePool] 池子已满，无法预加载: ${src}`);
        return false;
      }
    }

    this.preloadQueue.add(src);
    
    try {
      console.log(`[IFramePool] 开始预加载: ${src}`);
      await this.createIFrame(src);
      this.preloadQueue.delete(src);
      return true;
    } catch (error) {
      this.preloadQueue.delete(src);
      console.error(`[IFramePool] 预加载失败: ${src}`, error);
      return false;
    }
  }

  /**
   * 批量预加载 iframe
   * @param urls - 要预加载的 URL 列表
   * @returns Promise<number> 成功预加载的数量
   */
  async preloadBatch(urls: string[]): Promise<number> {
    const promises = urls.map(url => this.preloadIFrame(url));
    const results = await Promise.allSettled(promises);
    
    const successCount = results.filter(result => 
      result.status === 'fulfilled' && result.value === true
    ).length;
    
    console.log(`[IFramePool] 批量预加载完成: ${successCount}/${urls.length}`);
    return successCount;
  }

  /**
   * 释放 iframe（标记为未使用）
   * @param src - iframe 源地址
   */
  releaseIFrame(src: string): void {
    const item = this.pool.get(src);
    if (item) {
      item.inUse = false;
      item.lastUsedAt = Date.now();
      
      // 隐藏 iframe
      if (item.iframe.parentNode) {
        item.iframe.style.display = 'none';
      }
      
      console.log(`[IFramePool] 释放 iframe: ${src}`);
    }
  }

  /**
   * 移除 iframe 从池子中
   * @param src - iframe 源地址
   */
  removeIFrame(src: string): void {
    const item = this.pool.get(src);
    if (item) {
      // 从 DOM 中移除
      if (item.iframe.parentNode) {
        item.iframe.parentNode.removeChild(item.iframe);
      }
      
      this.pool.delete(src);
      console.log(`[IFramePool] 移除 iframe: ${src}`);
    }
  }

  /**
   * 清理空闲的 iframe
   */
  cleanup(): void {
    const now = Date.now();
    const toRemove: string[] = [];

    for (const [src, item] of this.pool.entries()) {
      if (!item.inUse && (now - item.lastUsedAt) > this.config.maxIdleTime) {
        toRemove.push(src);
      }
    }

    toRemove.forEach(src => this.removeIFrame(src));
    
    if (toRemove.length > 0) {
      console.log(`[IFramePool] 清理了 ${toRemove.length} 个空闲 iframe`);
    }
  }

  /**
   * 清空整个池子
   */
  clear(): void {
    const urls = Array.from(this.pool.keys());
    urls.forEach(src => this.removeIFrame(src));
    console.log(`[IFramePool] 清空池子，移除了 ${urls.length} 个 iframe`);
  }

  /**
   * 获取池子状态信息
   */
  getStatus() {
    const items = Array.from(this.pool.values());
    return {
      total: this.pool.size,
      inUse: items.filter(item => item.inUse).length,
      loaded: items.filter(item => item.loadState === 'loaded').length,
      loading: items.filter(item => item.loadState === 'loading').length,
      error: items.filter(item => item.loadState === 'error').length,
      preloading: this.preloadQueue.size,
      maxSize: this.config.maxSize,
    };
  }

  /**
   * 销毁池子
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    
    this.clear();
    this.preloadQueue.clear();
    console.log('[IFramePool] 池子已销毁');
  }

  /**
   * 创建新的 iframe
   * @param src - iframe 源地址
   * @param container - 容器元素
   * @returns Promise<HTMLIFrameElement>
   */
  private async createIFrame(src: string, container?: HTMLElement): Promise<HTMLIFrameElement> {
    return new Promise((resolve, reject) => {
      const iframe = document.createElement('iframe');
      const now = Date.now();
      
      // 设置 iframe 属性
      iframe.src = src;
      iframe.className = 'w-full h-full border-0';
      iframe.sandbox.add(
        'allow-same-origin',
        'allow-scripts', 
        'allow-forms',
        'allow-popups',
        'allow-modals',
        'allow-top-navigation'
      );
      iframe.title = `IFrame Pool - ${src}`;
      
      // 如果没有容器，隐藏 iframe（预加载模式）
      if (!container) {
        iframe.style.display = 'none';
        document.body.appendChild(iframe);
      } else {
        container.appendChild(iframe);
      }

      // 创建池子项
      const poolItem: IFramePoolItem = {
        iframe,
        src,
        inUse: !!container,
        loaded: false,
        createdAt: now,
        lastUsedAt: now,
        loadState: 'loading',
      };

      this.pool.set(src, poolItem);

      // 设置超时
      const timeout = setTimeout(() => {
        poolItem.loadState = 'error';
        poolItem.error = new Error('IFrame 加载超时');
        reject(poolItem.error);
      }, this.config.preloadTimeout);

      // 加载成功处理
      const handleLoad = () => {
        clearTimeout(timeout);
        poolItem.loaded = true;
        poolItem.loadState = 'loaded';
        
        iframe.removeEventListener('load', handleLoad);
        iframe.removeEventListener('error', handleError);
        
        console.log(`[IFramePool] iframe 加载成功: ${src}`);
        resolve(iframe);
      };

      // 加载失败处理
      const handleError = (event: Event) => {
        clearTimeout(timeout);
        poolItem.loadState = 'error';
        poolItem.error = new Error('IFrame 加载失败');
        
        iframe.removeEventListener('load', handleLoad);
        iframe.removeEventListener('error', handleError);
        
        console.error(`[IFramePool] iframe 加载失败: ${src}`, event);
        reject(poolItem.error);
      };

      iframe.addEventListener('load', handleLoad);
      iframe.addEventListener('error', handleError);
    });
  }

  /**
   * 启动自动清理
   */
  private startAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }
}

/**
 * 全局 iframe 池实例
 */
export const globalIFramePool = new IFramePool({
  maxSize: 10,
  maxIdleTime: 5 * 60 * 1000, // 5分钟
  preloadTimeout: 30 * 1000, // 30秒
  autoCleanup: true,
  cleanupInterval: 60 * 1000, // 1分钟
});
