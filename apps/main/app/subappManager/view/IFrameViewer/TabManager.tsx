import { useState, useCallback, useEffect } from 'preact/hooks';
import { globalIFramePool } from './IFramePool';
import { PooledIFrameViewer } from './PooledIFrameViewer';

export interface TabItem {
  /** tab ID */
  id: string;
  /** tab 标题 */
  title: string;
  /** iframe URL */
  url: string;
  /** 是否激活 */
  active: boolean;
  /** 是否已加载 */
  loaded?: boolean;
  /** 图标 */
  icon?: string;
}

export interface TabManagerProps {
  /** 初始 tabs */
  initialTabs?: TabItem[];
  /** 是否启用预加载 */
  enablePreload?: boolean;
  /** 最大 tab 数量 */
  maxTabs?: number;
}

/**
 * Tab 管理器组件
 * 使用 iframe pool 实现快速 tab 切换
 * 
 * 功能特性:
 * - 多 tab 管理
 * - iframe 池化复用
 * - 预加载支持
 * - 快速切换
 * - tab 关闭和新增
 */
export const TabManager = ({
  initialTabs = [],
  enablePreload = true,
  maxTabs = 10
}: TabManagerProps) => {
  const [tabs, setTabs] = useState<TabItem[]>(initialTabs);
  const [activeTabId, setActiveTabId] = useState<string>(
    initialTabs.find(tab => tab.active)?.id || initialTabs[0]?.id || ''
  );

  // 获取当前激活的 tab
  const activeTab = tabs.find(tab => tab.id === activeTabId);

  /**
   * 切换到指定 tab
   */
  const switchToTab = useCallback((tabId: string) => {
    setActiveTabId(tabId);
    setTabs(prevTabs => 
      prevTabs.map(tab => ({
        ...tab,
        active: tab.id === tabId
      }))
    );
    
    console.log(`[TabManager] 切换到 tab: ${tabId}`);
  }, []);

  /**
   * 添加新 tab
   */
  const addTab = useCallback((newTab: Omit<TabItem, 'active'>) => {
    if (tabs.length >= maxTabs) {
      console.warn(`[TabManager] 已达到最大 tab 数量: ${maxTabs}`);
      return;
    }

    const tabWithActive: TabItem = {
      ...newTab,
      active: true
    };

    setTabs(prevTabs => [
      ...prevTabs.map(tab => ({ ...tab, active: false })),
      tabWithActive
    ]);
    
    setActiveTabId(newTab.id);
    
    console.log(`[TabManager] 添加新 tab: ${newTab.id}`);
  }, [tabs.length, maxTabs]);

  /**
   * 关闭 tab
   */
  const closeTab = useCallback((tabId: string) => {
    const tabToClose = tabs.find(tab => tab.id === tabId);
    if (!tabToClose) return;

    // 从池子中释放 iframe
    globalIFramePool.releaseIFrame(tabToClose.url);

    setTabs(prevTabs => {
      const newTabs = prevTabs.filter(tab => tab.id !== tabId);
      
      // 如果关闭的是当前激活的 tab，切换到下一个
      if (tabId === activeTabId && newTabs.length > 0) {
        const nextTab = newTabs[0];
        nextTab.active = true;
        setActiveTabId(nextTab.id);
      }
      
      return newTabs;
    });
    
    console.log(`[TabManager] 关闭 tab: ${tabId}`);
  }, [tabs, activeTabId]);

  /**
   * 预加载所有 tab 的 URL
   */
  const preloadAllTabs = useCallback(async () => {
    if (!enablePreload) return;
    
    const urls = tabs.map(tab => tab.url);
    if (urls.length > 0) {
      console.log(`[TabManager] 开始预加载 ${urls.length} 个 tab`);
      await globalIFramePool.preloadBatch(urls);
    }
  }, [tabs, enablePreload]);

  // 预加载所有 tabs
  useEffect(() => {
    preloadAllTabs();
  }, [preloadAllTabs]);

  /**
   * 渲染 tab 标签栏
   */
  const renderTabBar = () => {
    return (
      <div className="flex items-center bg-gray-800 border-b border-gray-600 overflow-x-auto">
        {tabs.map(tab => (
          <div
            key={tab.id}
            className={`
              flex items-center px-4 py-2 min-w-0 cursor-pointer border-r border-gray-600
              ${tab.active 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }
            `}
            onClick={() => switchToTab(tab.id)}
          >
            {/* 图标 */}
            {tab.icon && (
              <span className="mr-2 text-sm">{tab.icon}</span>
            )}
            
            {/* 标题 */}
            <span className="truncate max-w-32">{tab.title}</span>
            
            {/* 关闭按钮 */}
            <button
              className="ml-2 w-4 h-4 flex items-center justify-center rounded hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                closeTab(tab.id);
              }}
              title="关闭"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        ))}
        
        {/* 添加 tab 按钮 */}
        {tabs.length < maxTabs && (
          <button
            className="flex items-center justify-center w-10 h-10 text-gray-400 hover:text-white hover:bg-gray-600"
            onClick={() => {
              const newTabId = `tab-${Date.now()}`;
              addTab({
                id: newTabId,
                title: `新标签页 ${tabs.length + 1}`,
                url: 'https://www.baidu.com',
                icon: '🌐'
              });
            }}
            title="新建标签页"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        )}
      </div>
    );
  };

  /**
   * 渲染池子状态信息
   */
  const renderPoolStatus = () => {
    const status = globalIFramePool.getStatus();
    
    return (
      <div className="flex items-center gap-4 px-4 py-2 bg-gray-900 text-xs text-gray-400 border-b border-gray-600">
        <span>池子状态:</span>
        <span>总计: {status.total}</span>
        <span>使用中: {status.inUse}</span>
        <span>已加载: {status.loaded}</span>
        <span>加载中: {status.loading}</span>
        <span>预加载中: {status.preloading}</span>
        {status.error > 0 && <span className="text-red-400">错误: {status.error}</span>}
      </div>
    );
  };

  /**
   * 渲染内容区域
   */
  const renderContent = () => {
    if (!activeTab) {
      return (
        <div className="flex-1 flex items-center justify-center text-gray-400">
          <div className="text-center">
            <p className="text-lg mb-2">没有打开的标签页</p>
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              onClick={() => {
                addTab({
                  id: 'tab-1',
                  title: '百度',
                  url: 'https://www.baidu.com',
                  icon: '🔍'
                });
              }}
            >
              创建新标签页
            </button>
          </div>
        </div>
      );
    }

    // 模拟 service 对象
    const mockService = {
      subAppConfig: {
        url: activeTab.url,
        name: activeTab.title,
        props: null
      },
      handleActivated: (data: any) => {
        console.log(`[TabManager] Tab ${activeTab.id} 激活:`, data);
      }
    };

    return (
      <div className="flex-1">
        <PooledIFrameViewer
          service={mockService as any}
          pool={globalIFramePool}
          enablePreload={enablePreload}
        />
      </div>
    );
  };

  return (
    <div className="w-full h-full flex flex-col bg-gray-800">
      {/* Tab 标签栏 */}
      {renderTabBar()}
      
      {/* 池子状态 */}
      {renderPoolStatus()}
      
      {/* 内容区域 */}
      {renderContent()}
    </div>
  );
};

export default TabManager;
