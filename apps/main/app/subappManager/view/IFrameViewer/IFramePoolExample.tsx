import { useState, useEffect } from 'preact/hooks';
import { TabManager, type TabItem } from './TabManager';
import { globalIFramePool } from './IFramePool';

/**
 * IFrame Pool 使用示例组件
 * 展示如何使用 iframe pool 进行快速 tab 切换
 */
export const IFramePoolExample = () => {
  const [poolStats, setPoolStats] = useState(globalIFramePool.getStatus());

  // 定义初始 tabs
  const initialTabs: TabItem[] = [
    {
      id: 'baidu',
      title: '百度',
      url: 'https://www.baidu.com',
      active: true,
      icon: '🔍'
    },
    {
      id: 'github',
      title: 'GitHub',
      url: 'https://github.com',
      active: false,
      icon: '🐙'
    },
    {
      id: 'stackoverflow',
      title: 'Stack Overflow',
      url: 'https://stackoverflow.com',
      active: false,
      icon: '📚'
    }
  ];

  // 定期更新池子状态
  useEffect(() => {
    const interval = setInterval(() => {
      setPoolStats(globalIFramePool.getStatus());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  /**
   * 清理池子
   */
  const clearPool = () => {
    globalIFramePool.clear();
    setPoolStats(globalIFramePool.getStatus());
  };

  /**
   * 手动预加载一些 URL
   */
  const preloadUrls = async () => {
    const urls = [
      'https://www.google.com',
      'https://www.bing.com',
      'https://duckduckgo.com'
    ];
    
    console.log('开始预加载 URLs...');
    const successCount = await globalIFramePool.preloadBatch(urls);
    console.log(`预加载完成: ${successCount}/${urls.length}`);
    setPoolStats(globalIFramePool.getStatus());
  };

  return (
    <div className="w-full h-full flex flex-col bg-gray-900">
      {/* 标题和控制面板 */}
      <div className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-600">
        <div>
          <h1 className="text-xl font-bold text-white mb-1">IFrame Pool 演示</h1>
          <p className="text-sm text-gray-400">
            使用 iframe pool 实现快速 tab 切换，提高页面性能
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={preloadUrls}
          >
            预加载示例 URLs
          </button>
          
          <button
            className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
            onClick={clearPool}
          >
            清理池子
          </button>
        </div>
      </div>

      {/* 池子详细状态 */}
      <div className="px-4 py-2 bg-gray-850 border-b border-gray-600">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 text-sm">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-400">{poolStats.total}</div>
            <div className="text-gray-400">总计</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-green-400">{poolStats.inUse}</div>
            <div className="text-gray-400">使用中</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-blue-400">{poolStats.loaded}</div>
            <div className="text-gray-400">已加载</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-yellow-400">{poolStats.loading}</div>
            <div className="text-gray-400">加载中</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-purple-400">{poolStats.preloading}</div>
            <div className="text-gray-400">预加载中</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-red-400">{poolStats.error}</div>
            <div className="text-gray-400">错误</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-bold text-gray-400">{poolStats.maxSize}</div>
            <div className="text-gray-400">最大容量</div>
          </div>
        </div>
      </div>

      {/* Tab 管理器 */}
      <div className="flex-1">
        <TabManager
          initialTabs={initialTabs}
          enablePreload={true}
          maxTabs={8}
        />
      </div>

      {/* 使用说明 */}
      <div className="p-4 bg-gray-800 border-t border-gray-600">
        <div className="text-sm text-gray-400">
          <h3 className="font-semibold text-white mb-2">使用说明:</h3>
          <ul className="space-y-1 list-disc list-inside">
            <li>点击标签页可以快速切换，iframe 会被复用以提高性能</li>
            <li>点击 "+" 按钮可以添加新的标签页</li>
            <li>点击标签页上的 "×" 按钮可以关闭标签页</li>
            <li>池子会自动预加载和管理 iframe，减少切换时的加载时间</li>
            <li>空闲的 iframe 会在 5 分钟后自动清理</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default IFramePoolExample;
