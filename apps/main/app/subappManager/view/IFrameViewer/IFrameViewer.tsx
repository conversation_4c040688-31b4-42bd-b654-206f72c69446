import type { useMainViewerService } from '#app/subappManager/service/mainViewer.service';
import SubAppIFrame from '#app/subappManager/view/IFrameViewer/SubAppIFrame';

export interface IWuJieViewerProps {
  service: ReturnType<typeof useMainViewerService>;
}

const IFrameViewer = ({ service }: IWuJieViewerProps) => {
  return (
    <SubAppIFrame
      className="w-full h-full flex-1"
      app={service.subAppConfig}
      reload={service.reload}
      onActivated={service.handleActivated}
    />
  );
};

export default IFrameViewer;
