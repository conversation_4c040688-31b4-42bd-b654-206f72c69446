import type { XCefSDK } from '@ssglauncher/sdk';
import type { Observable } from 'rxjs';
import type { IGameManagerRepo } from '#domains/gameManager/repository/gameManager.repo';
import type { GameStatus, IDownloadManagerRepo } from '#domains/gameManager/repository/downloadManager.repo';
import { listenerToObserver } from '@ssglauncher/operators';
import { BehaviorSubject, ReplaySubject, from, lastValueFrom, map, shareReplay, switchMap } from 'rxjs';
import { BSRepository, UseRepository } from '@banshee/ex-banshee';
import { InjectPlugin, Repository } from '@banshee/ex-banshee';
import { EDownloadType } from '#domains/gameManager/repository/downloadManager.repo';
import { XCefSDKSymbol } from '#domains/infra/xcefSdk.plugin';
import { DownloadState } from '#common/util/downloadState';

type TXCef = Awaited<ReturnType<typeof XCefSDK>>;

@Repository('DownloadManager')
export class DownloadManagerRepo extends BSRepository implements IDownloadManagerRepo {
  private xcef$: ReplaySubject<TXCef> = new ReplaySubject(1);
  private currentGameStatus$: BehaviorSubject<GameStatus>;
  private currentPreGameStatus$: BehaviorSubject<GameStatus>;

  private windowActionTypes!: Awaited<ReturnType<typeof XCefSDK>>['GameModule']['CreateGameTaskType'];

  @UseRepository('GameManager')
  private readonly gameManagerRepo!: IGameManagerRepo;

  constructor(
    @InjectPlugin(XCefSDKSymbol)
    xcefPlugin: typeof XCefSDK,
  ) {
    super();

    this.currentGameStatus$ = new BehaviorSubject<GameStatus>({} as any);
    this.currentPreGameStatus$ = new BehaviorSubject<GameStatus>({} as any);
    this.createDownloadStatusListener();
    this.init(xcefPlugin);
  }

  currentGameStatus() {
    return this.currentGameStatus$.asObservable();
  }

  currentPreGameStatus() {
    return this.currentPreGameStatus$.asObservable();
  }

  resetDownloadStatus() {
    this.currentGameStatus$.next({
      id: '',
      currentVersion: '',
      nextVersion: '',
      msg: '',
      errMsg: '',
      progress: 0,
      ui: DownloadState.UNDEFINED,
      downloadType: 0,
      isPreDownloadVisible: false,
      isPreDownloadUpdating: false,
    });
  }

  // eslint-disable-next-line max-params
  async createTask(
    appCode: string,
    action: keyof typeof this.windowActionTypes,
    isPreDownload?: boolean,
    startupOption?: string,
  ) {
    const xcef = await lastValueFrom(this.xcef$);

    console.log('xcef.gameModule.createGameTask', { appCode, action, isPreDownload, startupOption }); // FIXME: debug
    xcef.gameModule.createGameTask({ appCode, action, isPreDownload, startupOption });
  }

  async execGameTool(appCode: string, executePath: keyof typeof this.windowActionTypes) {
    const xcef = await lastValueFrom(this.xcef$);

    xcef.gameModule.execGameTool({ appCode, executePath });
  }

  private createDownloadStatusListener() {
    this.ready(xcef => listenerToObserver(xcef.gameModule.onGameDownloadStatusChanged))
      .pipe(
        map(value => ({
          id: value.appCode,
          currentVersion: value.currentVersion,
          nextVersion: value.nextVersion,
          msg: value.msg,
          errMsg: value.errMsg,
          progress: value.progress,
          ui: value.uiStage,
          downloadType: value.downloadType,
          isPreDownloadVisible: value.isPreDownloadVisible,
          isPreDownloadUpdating: value.isPreUpdateFormat,
        })),
      )
      .subscribe((v: GameStatus) => {
        switch (v.downloadType) {
          case EDownloadType.PreDownload:
            this.currentPreGameStatus$.next(v);
            break;
          default:
            this.currentGameStatus$.next(v);
            break;
        }
      });
  }

  private ready<T>(observer: (xcef: TXCef) => Observable<T>): Observable<T> {
    return this.xcef$.pipe(
      shareReplay(1),
      switchMap(xcef => observer(xcef)),
    );
  }

  private init(xcefPlugin: typeof XCefSDK) {
    from(xcefPlugin()).subscribe(this.xcef$);

    this.xcef$.subscribe(xcef => {
      this.windowActionTypes = xcef.GameModule.CreateGameTaskType;
    });
  }
}
