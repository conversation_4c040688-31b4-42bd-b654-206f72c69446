import type { TStartButtonProps } from '../hooks/useStartButtonState';
import { useState, useCallback } from 'preact/hooks';
import { ToolTipsBox } from '@ssglauncher/components';
import { Fragment } from 'preact/compat';
import { StartButton } from '../component/StartButton';
import { useMultiStateButtonService } from '../service/multiStateButton.service';
import { DownloadState } from '#common/util/downloadState';

interface MultiStateButtonProps {
  buttonState: TStartButtonProps;
  nextStage: (state: DownloadState) => void;
  onClick?: any;
  onHover?: (status: boolean) => void;
}

export function MultiStateButton({ buttonState, nextStage, onClick, onHover }: MultiStateButtonProps) {
  const service = useMultiStateButtonService(nextStage);
  const [isButtonHover, setIsButtonHover] = useState(false);
  const onBtnHover = value => {
    setIsButtonHover(value);
    onHover(value);
  };

  const renderToolTipsBox = useCallback(() => {
    if (isButtonHover) {
      return (
        <ToolTipsBox className="absolute z-10 text-center top-0 w-full left-0 translate-y-[-100%] mt--4px">
          {service.gameStatus?.errMsg}
        </ToolTipsBox>
      );
    }
    return null;
  }, [isButtonHover, service.gameStatus]);

  const isDownloadFailed =
    service.gameStatus?.ui === DownloadState.RETRY_DOWNLOAD ||
    service.gameStatus?.ui === DownloadState.RETRY_UPDATE ||
    service.gameStatus?.ui === DownloadState.RETRY_REPAIR ||
    service.gameStatus?.ui === DownloadState.RETRY_UNINSTALL;

  return (
    <Fragment>
      <StartButton
        id={service.gameStatus?.id}
        icon={buttonState.icon}
        iconClassName={buttonState.iconClassName}
        color={buttonState.color}
        title={buttonState.title}
        subTitle={service.subTitle}
        description={service.description}
        showDescription={buttonState.showDescription}
        progress={service.progress}
        showProgress={buttonState.showProgress}
        disabled={buttonState.disabled}
        onClick={onClick}
        onHover={onBtnHover}
      />
      {isDownloadFailed && renderToolTipsBox()}
    </Fragment>
  );
}
