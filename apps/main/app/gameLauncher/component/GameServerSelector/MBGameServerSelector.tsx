import { Icon } from '@ssglauncher/iconfont';
import { clsx } from 'clsx';
import { useTranslation } from 'react-i18next';
import { ToolTipsBox } from '@ssglauncher/components';
import { useMemo } from 'preact/hooks';
import { GAMEPAD_HIDE_UNSUPPORTED, GAMEPAD_NAV_GROUP, navAttrs } from '@ssglauncher/gamepad';
import { pingToStatus } from '#common/util/pintToStatus';

export interface MBGameServerSelectorProps {
  className?: string;
  name: string;
  onClick: () => void;
  ping?: number;
  loading: boolean;
}

function MBGameServerSelector({ className, onClick, ping, name, loading }: MBGameServerSelectorProps) {
  const { t } = useTranslation();
  const status = pingToStatus(ping);
  const serverText = useMemo(() => {
    if (loading) {
      return t('common:LOADING', '加载中...');
    }
    if (name) {
      return name;
    }
    return t('common:GAME_SERVER_SELECTION.DEFAULT_NAME', '请选择区服');
  }, [loading, name, t]);

  const handleClick = () => {
    if (loading) {
      return;
    }
    onClick();
  };

  return (
    <div
      {...navAttrs(navAttrs.index(GAMEPAD_NAV_GROUP.MAIN_BOTTOM_BAR, 1), 'x', {
        up: GAMEPAD_NAV_GROUP.GAMES_ZONE_EXTENSION,
      })}
      onClick={handleClick}
      className={clsx(
        'h-42px px-3 relative flex cursor-pointer rounded-1 group text-white-60 outline-none',
        'active:text-white-100 active:bg-white-12 hover:bg-white-8 hover:text-white-100',
        !GAMEPAD_HIDE_UNSUPPORTED && 'transition-colors duration-100',
        className,
      )}
    >
      <div className="relative z-10 flex items-center h-full">
        {name && (
          <div
            className={clsx('w-8px h-8px rounded-full shrink-0 mr-8px', {
              'bg-green': status === 'green',
              'bg-yellow-100': status === 'orange',
              'bg-error': status === 'error',
            })}
          />
        )}
        <div className="text-head h-18px leading-18px text-ellipsis whitespace-nowrap overflow-hidden max-w-188px">
          {serverText}
        </div>
        {loading ? (
          <Icon icon="refresh-2" className="w-16px h-16px ml-8px animate-spin" />
        ) : (
          <Icon icon="switch" className="w-16px h-16px ml-8px " />
        )}
      </div>
      {!!name && (
        <ToolTipsBox className="absolute bottom-44px right-0 w-fit !max-w-none whitespace-nowrap z-10 hidden group-hover:block animate-fadeIn">
          {name}
        </ToolTipsBox>
      )}
    </div>
  );
}

export { MBGameServerSelector };
