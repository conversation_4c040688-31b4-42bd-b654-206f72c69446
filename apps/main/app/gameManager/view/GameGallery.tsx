import type { TIcon } from '@ssglauncher/iconfont';
import { useTranslation } from 'react-i18next';
import { useGameGalleryService } from '../service/gameGallery.service';
import Selector from '../component/SortSelector';
import GameCard from '../component/GameCard';
import { SubAppLoading as Loading } from '#app/subappManager/component/SubAppLoading';

export interface TTab {
  id: string;
  label: string;
  icon: TIcon;
}

export function GameGallery() {
  const { t } = useTranslation();

  const service = useGameGalleryService();
  if (service.isFetching) {
    return <Loading />;
  }

  return (
    <div className="flex flex-col overflow-hidden h-full">
      <div className="w-full pb-sp12 bg-black-100">
        <div className="mx-auto px-sp40 flex items-center">
          <span className="mr-sp4 color-white-60">{t('GAMES:MANAGER.SORT_LABEL', '排序：')}</span>
          <Selector list={service.tabs} value={service.currentTab} onChange={service.handleChangeTab} />
        </div>
      </div>
      <div className="flex flex-wrap overflow-overlay px-sp40 pt-sp12 gap-x-14px gap-y-sp24">
        {service.gameList.map(item => (
          <GameCard
            key={item.id}
            cover={item.cover}
            title={item.name}
            desc={item.description}
            isStar={item.isStar}
            isSoon={item.isComingSoon}
            onClick={service.navigate(item)}
            onStarClick={service.handleFavourite(item)}
          />
        ))}
      </div>
    </div>
  );
}
