import type { SelectorItemProps } from './item';
import SelectorItem from './item';

interface TabItem extends SelectorItemProps {
  id: string;
}

interface SelectorProps {
  list: TabItem[];
  value: string;
  onChange?: (val: string) => void;
}

function Selector({ value, list = [], onChange }: SelectorProps) {
  const handleClick = (val: string) => () => {
    onChange?.(val);
  };

  return (
    <div className="flex items-center">
      {list.map(item => (
        <SelectorItem
          key={item.label}
          icon={item.icon}
          label={item.label}
          onClick={handleClick(item.id)}
          isActive={item.id === value}
        />
      ))}
    </div>
  );
}

export default Selector;
