/* eslint-disable complexity */
import { EG<PERSON>JobStage, <PERSON>Game<PERSON><PERSON> } from '@ssglauncher/server-api';
import { DownloadState } from './downloadState';

export function jobToUIStage(job?: EGameJob, jobStage?: EGameJobStage): DownloadState {
  // 游戏未下载 设置为 XGAME_UI_TASK_INSTALL，XGAME_UI_TASK_STATE_UNINITIALIZED
  if (job === EGameJob.INSTALL && jobStage === EGameJobStage.UNINITIALIZED) {
    return DownloadState.UN_DOWNLOADED;
  }

  // 下载中，状态置为 XGAME_UI_TASK_INSTALL，XGAME_UI_TASK_STATE_DOING
  if (job === EGameJob.INSTALL && jobStage === EGameJobStage.DOING) {
    return DownloadState.DOWNLOADING;
  }

  // 更新中，状态置为 XGAME_UI_TASK_UPDATE，XGAME_UI_TASK_STATE_DOING
  if (job === EGameJob.UPDATE && jobStage === EGameJobStage.DOING) {
    return DownloadState.UPDATING;
  }

  // 清理中，状态置为 XGAME_UI_TASK_CLEAN，XGAME_UI_TASK_STATE_DOING
  if (job === EGameJob.CLEAN && jobStage === EGameJobStage.DOING) {
    return DownloadState.CLEANING;
  }

  // 卸载中，状态置为 XGAME_UI_TASK_UNINSTALL，XGAME_UI_TASK_STATE_DOING
  if (job === EGameJob.UNINSTALL && jobStage === EGameJobStage.DOING) {
    return DownloadState.UNINSTALLING;
  }

  // 修复中，状态置为 XGAME_UI_TASK_REPAIR，XGAME_UI_TASK_STATE_DOING
  if (job === EGameJob.REPAIR && jobStage === EGameJobStage.DOING) {
    return DownloadState.REPAIRING;
  }

  // 下载暂停，状态置为 XGAME_UI_TASK_REPAIR，XGAME_UI_TASK_STATE_PAUSING
  if (job === EGameJob.INSTALL && jobStage === EGameJobStage.PAUSING) {
    return DownloadState.DOWNLOAD_PAUSE;
  }

  // 更新暂停，状态置为 XGAME_UI_TASK_UPDATE，XGAME_UI_TASK_STATE_PAUSING
  if (job === EGameJob.UPDATE && jobStage === EGameJobStage.PAUSING) {
    return DownloadState.UPDATE_PAUSE;
  }

  // 修复暂停，状态置为 XGAME_UI_TASK_REPAIR，XGAME_UI_TASK_STATE_PAUSING
  if (job === EGameJob.REPAIR && jobStage === EGameJobStage.PAUSING) {
    return DownloadState.REPAIR_PAUSE;
  }

  // 需要更新，状态置为 XGAME_UI_TASK_UPDATE，XGAME_UI_TASK_STATE_UNINITIALIZED
  if (job === EGameJob.UPDATE && jobStage === EGameJobStage.UNINITIALIZED) {
    return DownloadState.SHOULD_UPDATE;
  }

  // 需要更新，状态置为 XGAME_UI_TASK_UPDATE，XGAME_UI_TASK_STATE_UNINITIALIZED
  if (job === EGameJob.UPDATE && jobStage === EGameJobStage.PRE_DOING) {
    return DownloadState.SHOULD_UPDATE;
  }

  // 抢先收藏，状态置为 XGAME_UI_TASK_CREATEGAME，XGAME_UI_TASK_STATE_UNINITIALIZED
  if (job === EGameJob.CREATEGAME && jobStage === EGameJobStage.UNINITIALIZED) {
    return DownloadState.CAN_COLLECT;
  }

  // 抢先收藏，状态置为 XGAME_UI_TASK_CREATEGAME，XGAME_UI_TASK_STATE_DOING
  if (job === EGameJob.CREATEGAME && jobStage === EGameJobStage.DOING) {
    return DownloadState.COMING_SOON;
  }

  // 下载重试，状态置为 XGAME_UI_TASK_INSTALL，XGAME_UI_TASK_STATE_FAILED
  if (job === EGameJob.INSTALL && jobStage === EGameJobStage.FAILED) {
    return DownloadState.RETRY_DOWNLOAD;
  }

  // 更新重试，状态置为 XGAME_UI_TASK_UPDATE，XGAME_UI_TASK_STATE_FAILED
  if (job === EGameJob.UPDATE && jobStage === EGameJobStage.FAILED) {
    return DownloadState.RETRY_UPDATE;
  }

  // 修复重试，状态置为 XGAME_UI_TASK_REPAIR，XGAME_UI_TASK_STATE_FAILED
  if (job === EGameJob.REPAIR && jobStage === EGameJobStage.FAILED) {
    return DownloadState.RETRY_REPAIR;
  }

  // 卸载重试，状态置为 XGAME_UI_TASK_UNINSTALL，XGAME_UI_TASK_STATE_FAILED
  if (job === EGameJob.UNINSTALL && jobStage === EGameJobStage.FAILED) {
    return DownloadState.RETRY_UNINSTALL;
  }

  // 下载中，进入了排队队列，状态置为XGAME_UI_TASK_INSTALL,  XGAME_UI_TASK_STATE_WAITING
  if (job === EGameJob.INSTALL && jobStage === EGameJobStage.WAITING) {
    return DownloadState.DOWNLOAD_WAITING;
  }

  // 游戏检查更新，进入游戏更新排队状态，状态置为XGAME_UI_TASK_UPDATE, XGAME_UI_TASK_STATE_WAITING
  if (job === EGameJob.UPDATE && jobStage === EGameJobStage.WAITING) {
    return DownloadState.UPDATE_WAITING;
  }

  // 游戏修复，进入游戏修复排队状态，状态置为XGAME_UI_TASK_REPAIR, XGAME_UI_TASK_STATE_WAITING
  if (job === EGameJob.REPAIR && jobStage === EGameJobStage.WAITING) {
    return DownloadState.REPAIR_WAITING;
  }

  // 游戏从下载完成，进入了开始游戏状态，状态置为XGAME_UI_TASK_NONE，XGAME_UI_TASK_STATE_UNINIT
  if ((job === EGameJob.NONE || job === EGameJob.PREUPDATE) && jobStage === EGameJobStage.UNINITIALIZED) {
    return DownloadState.READY;
  }

  // 游戏正在运行中, 不支持多开
  if (job === EGameJob.NONE && jobStage === EGameJobStage.DOING) {
    return DownloadState.RUNNING;
  }

  return DownloadState.UNDEFINED;
}
