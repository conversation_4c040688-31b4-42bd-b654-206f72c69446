import type { VNode } from 'preact';
import { useEffect, useRef, useState } from 'preact/hooks';

/** 延迟渲染组件 */
export function renderDelayComp(component: VNode, delay: number) {
  const [visible, setVisible] = useState(false);
  const timerId = useRef(null);

  useEffect(() => {
    timerId.current = setTimeout(() => {
      setVisible(true);
    }, delay);

    return () => {
      if (timerId.current) {
        clearTimeout(timerId.current);
        timerId.current = null;
      }
    };
  }, []);

  return visible && component;
}
