import type { HeaderLayout } from '../../app/window/component/Header';
import type { GameBar } from '#app/gameManager/view/GameBar';
import type { PropsWithChildren } from 'react';

export interface ApplicationLayoutProps {
  header: ReturnType<typeof HeaderLayout>;
  aside: ReturnType<typeof GameBar>;
}

export function ApplicationLayout({ aside, header, children }: PropsWithChildren<ApplicationLayoutProps>) {
  (console as any).pin('ApplicationLayout');
  return (
    <div className="w-full h-full relative mx-auto flex">
      {aside}
      <div className="flex-1 flex-col relative overflow-hidden">
        {header}
        {children}
      </div>
    </div>
  );
}
