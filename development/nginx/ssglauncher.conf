server {
  listen 8803;
  server_name localhost;

  location /games {
    proxy_pass http://127.0.0.1:3000/games;
  }

  location /game {
      proxy_pass http://127.0.0.1:8081/games;
  }

  location /additional {
    proxy_pass http://127.0.0.1:3004/additional;
  }

  location /account {
    proxy_pass http://127.0.0.1:3008/account;
  }

  location /gp-account {
    proxy_pass http://127.0.0.1:3003/account;
  }

  location /locales {
    alias /{your_workspace}/ssglauncher-i18n-static/json; ## 替换为真实路径
    autoindex on;
    expires 1d;
  }

  location / {
    proxy_pass http://127.0.0.1:5173;
  }

}
