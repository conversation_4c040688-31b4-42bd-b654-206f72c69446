param(
  <# 本地资源路径 #>
  [string]$buildEnv="qa",
  <# 本地资源路径 #>
  [string]$localPath,
  <# 本地资源路径 #>
  [string]$bucket="ssg-launcher-g",
  <# minio的bucket名 #>
  [string]$remoteBasePath,
  <# minio的路径 #>
  [string[]]$namespaces
  <# 需要上传的国际化namespaces(json名) #>
)
$stBucket='ssg-st-g'
function Copy-TargetToSyncDirectory{
  param (
   [string]$sourceFile
  )
  $lang = (Get-Item(Split-Path $sourceFile -Parent)).Name
  # rsync命令的目标目录
  $syncDirectory = "third-login-redirect/$buildEnv/locales/$lang"
  if (-not (Test-Path $syncDirectory)) {
    New-Item -ItemType Directory -Path $syncDirectory -Force
    Write-Host "Created directory: $syncDirectory"
  }
  Copy-Item -Path $sourceFile -Destination $syncDirectory
}

Get-ChildItem -Recurse -File -Path $localPath | Where-Object {
  $ns = $_.Name -replace '\.json', '';
  return $namespaces -contains $ns;
} | % {
  $fullPath = $_.FullName
  $relativePath = (Resolve-Path -Path $fullPath -Relative) -replace '^\./', ''
  $remotePath = (Resolve-Path -Path $fullPath -RelativeBasePath $localPath -Relative) -replace '^\./', ''
  $key = "$remoteBasePath/$remotePath"
  Write-Host "miniocli put $bucket $relativePath $key"
  Start-Process -FilePath "./miniocli" -ArgumentList "put", "$bucket", "$relativePath", "$key"  -Environment @{ "MINIO_ENDPOINT" = "ssg-intranet-dl.seasungame.com:9000" } -Wait
  if ($buildEnv -eq "prod") {
    Write-Host "miniocli put $stBucket $relativePath $key"
    Start-Process -FilePath "./miniocli" -ArgumentList "put", "$stBucket", "$relativePath", "$key"  -Environment @{ "MINIO_ENDPOINT" = "ssg-intranet-dl.seasungame.com:9000" } -Wait
  }
  Write-Host "had uploaded to $key"
  Copy-TargetToSyncDirectory $relativePath
}


if ($buildEnv -eq "prod") {
  Write-Host "going to sync $sourceFile to $syncDirectory"
  & rsync -av third-login-redirect rsync://ssgweb@***********:8731/ssgweb
  & rsync -av third-login-redirect rsync://ssgweb@***********:8733/ssgweb
  Write-Host "sync finished"
}