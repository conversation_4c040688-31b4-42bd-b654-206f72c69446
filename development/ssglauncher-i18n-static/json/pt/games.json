{"COMMON": {"LOADING": "Carregando..."}, "DEFAULT_TEXT": "Ops! A página está indisponível. Tente recarregá-la.", "MANAGER": {"NEED_ONE_GAME": "Adicione pelo menos um jogo", "SORT_HOT": "Mais popular", "SORT_LABEL": "Ordenar por:", "SORT_LETTER": "A-Z", "SORT_NEW": "<PERSON><PERSON>e"}, "ZONE": {"CONFIGURE_TITLE": "Configuração recomendada para o jogo", "DEFAULT_NOTIFICATION": "Um mundo de jogos emocionante espera por você!", "EXTENSION_CARD.RECORD_CARD.NO_GAME_ROLE": "Informação sobre o personagem não encontrada", "EXTENSION_CARD.RECORD_CARD.NO_RECORD": "Sem registros de pareamento", "EXTENSION_CARD.RECORD_CARD.NO_SERVER_ROLE": "Sem personagem selecionada", "EXTENSION_CARD1": "Conteúdo em alta", "EXTENSION_CARD2": "Perfil", "GAME_RECORD.DAMAGE": "<PERSON><PERSON>", "GAME_RECORD.DAMAGE_TAKEN": "<PERSON><PERSON>", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.BOSS": "Alvo Colossial", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.CAMP_DEDUCT": "Mortes de equipe", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.CLOSED_ZONE_TASK_POINT": "Pontos de Zona Restrita", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.DEFENSE_INSTALLATION": "Instalação Defensiva", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.EIC_JAR": "Espécime de Corite", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.EIC_NPC": "Atacante Contaminado por Corite ", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.ELITE": "Atacantes de 3ª Geração", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.EVACUATION_SUCCESS": "Missão Concluída", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.GIANT_FORT": "Torreta grande", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.GIANT_INSTALLATION": "Instalação Colossal", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.KILL_HELL": "HELLCAT", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.KONSTANSTIN": "CONSTANTINE", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.MAKALU": "MAKALU", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.MINECORE": "<PERSON><PERSON><PERSON><PERSON>", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.MISSION_ABORTED": "Missão Fracassada", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.REINFORCEMENT": "Nave de Tropas/<PERSON><PERSON>", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.SCENE_MISC": "Atacantes de 2ª Geração", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.STRATUM_SHATTERER": "Extrator de Corite", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.TEAM_KILL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GAME_RECORD.DATA_ITEM.RECORD_DETAIL.TITLE": "Pts de missão", "GAME_RECORD.DEFAULT_RANK": "Bronze V", "GAME_RECORD.DIRECT_DAMAGE": "<PERSON><PERSON>", "GAME_RECORD.EVACUATIONS": "Missão Completa", "GAME_RECORD.GAME_RESULT.DEFAULT": "Desconhecido", "GAME_RECORD.GAME_RESULT.DEFEAT": "DERROTA", "GAME_RECORD.GAME_RESULT.DRAW": "EMPATE", "GAME_RECORD.GAME_RESULT.VICTORY": "VITÓRIA", "GAME_RECORD.KILL": "Eliminações", "GAME_RECORD.KILLED": "<PERSON><PERSON><PERSON>", "GAME_RECORD.LOGIN_BUTTON_TEXT": "<PERSON><PERSON><PERSON>", "GAME_RECORD.LOGIN_TIPS": "Conecte-se à conta para ver registros", "GAME_RECORD.MASK.GET_ROLE_BUTTON_LOADING_TEXT": "Obtendo", "GAME_RECORD.MASK.GET_ROLE_BUTTON_TEXT": "Obter agora", "GAME_RECORD.MASK.GET_ROLE_TIPS": "Após obter informação do personagem, exibe desempenho", "GAME_RECORD.MAX_UNIT": "Dez mil", "GAME_RECORD.PILOTING_DURATION": "Duração de Pilotagem", "GAME_RECORD.PLAY_TYPE.ACE_ARENA": "ARENA ÁS", "GAME_RECORD.PLAY_TYPE.BATTLEFIELD": "OPERAÇÃO VERGE", "GAME_RECORD.PLAY_TYPE.DEFAULT_MODE": "Desconhecido", "GAME_RECORD.PLAY_TYPE.MASHMAK": "MASHMAK", "GAME_RECORD.POINT": "Pontos de Mérito", "GAME_RECORD.RECORD_DETAIL.DIFFICULTY.CHALLENGE": "Extremo", "GAME_RECORD.RECORD_DETAIL.DIFFICULTY.HARD": "Dif<PERSON><PERSON>l", "GAME_RECORD.RECORD_DETAIL.DIFFICULTY.NORMAL": "Normal", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.GAME_DURATION": "Duração", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.MAP_NAME": "Nome do Mapa", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.PLAY_MODE": "Modo de Jogabilidade", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.PLAY_TYPE": "<PERSON><PERSON>", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.RANK": "Classificação", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.RESULT": "Resul<PERSON><PERSON>", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.SCORE": "Pontuação", "GAME_RECORD.RECORD_DETAIL.OVERVIEW.SCORES": "Pontuação", "GAME_RECORD.RECORD_DETAIL.PLAY_MODE.MULTI_MODE": "Multijogador", "GAME_RECORD.RECORD_DETAIL.PLAY_MODE.SINGLE_MODE": "Solo", "GAME_RECORD.RECORD_DETAIL.RANK_RESULT.DEFAULT": "Desconhecido", "GAME_RECORD.RECORD_DETAIL.RANK_RESULT.NORMAL_RANK": "N.º{{rank}}", "GAME_RECORD.RECORD_DETAIL.TABLE_HEADER.DEFAULT": "Desconhecido", "GAME_RECORD.RECORD_DETAIL.TABLE_HEADER.LOSER": "<PERSON><PERSON><PERSON>", "GAME_RECORD.RECORD_DETAIL.TABLE_HEADER.WINNER": "<PERSON><PERSON><PERSON>", "GAME_RECORD.RECORD_DETAIL.TABLE_NO_RECORD": "Sem registros de pareamento na temporada atual", "GAME_RECORD.REPAIR": "Reparo", "GAME_RECORD.RESULT.ABORTED": "Missão Fracassada", "GAME_RECORD.RESULT.EVACUATION": "Missão Concluída", "GAME_RECORD.SEASON.AIRDROP_EQUIP_COUNT": "Entregas Aéreas de Armas", "GAME_RECORD.SEASON.AIRDROP_SUPPORT_COUNT": "Entregas Aéreas de Dispositivos Auxiliares", "GAME_RECORD.SEASON.AIRDROP_VEHICLE_COUNT": "Planador de Entrega Aérea", "GAME_RECORD.SEASON.ASSIST": "Total de Assistências", "GAME_RECORD.SEASON.AVG_KILLS": "Média de Eliminações", "GAME_RECORD.SEASON.AVG_POINTS": "<PERSON><PERSON>dia de Pontos de Mérito", "GAME_RECORD.SEASON.AVG_SURVIVABILITY": "Tempo Médio de Sobrevivência", "GAME_RECORD.SEASON.BEST_CLOSER": "Finalizador Superior", "GAME_RECORD.SEASON.BEST_DEFENSE": "<PERSON><PERSON>", "GAME_RECORD.SEASON.BEST_SPIKER": "<PERSON><PERSON>", "GAME_RECORD.SEASON.BEST_SUPPORT": "<PERSON><PERSON>", "GAME_RECORD.SEASON.BEST_TACTICS": "<PERSON><PERSON>", "GAME_RECORD.SEASON.CONCENTRATION_EFFECTIVENESS": "Eficiência de Fogo Concentrado", "GAME_RECORD.SEASON.DAMAGE": "Dano Total", "GAME_RECORD.SEASON.DAMAGE_CONTRIBUTION": "Total de Dano Ajustado", "GAME_RECORD.SEASON.DAMAGE_EFFECTIVENESS": "Eficiência de DPS", "GAME_RECORD.SEASON.DAMAGE_TOLERANCE": "<PERSON><PERSON>", "GAME_RECORD.SEASON.DATA_INTRO.ACE_ARENA": "[\n  [\"Taxa de Participação de Equipe\",\"(Assistências + eliminações) / eliminações de equipe\"],\n  [\"Eficiência de DPS\",\"Dano ajustado / duração da partida\"],\n  [\"Eficiência de Fogo Concentrado\",\"Eficiência de fogo concentrado / (<PERSON><PERSON> / (Eliminações + Assistências))\"],\n  [\"Dano Recebido\",\"Dano recebido / Duração da partida\"],\n  [\"Reparo Realizado\",\"Reparo / Duração da partida\"]\n]", "GAME_RECORD.SEASON.DATA_INTRO.BATTLEFIELD": "[\n  [\"Taxa de Participação de Equipe\",\"(Assistências + eliminações) / eliminações de equipe\"],\n  [\"Eficiência de DPS\",\"Dano ajustado / duração da partida\"],\n  [\"Eficiência de Fogo Concentrado\",\"Eficiência de fogo concentrado / (<PERSON><PERSON> / (Eliminações + Assistências))\"],\n  [\"Dano Recebido\",\"Dano recebido / Duração da partida\"],\n  [\"Reparo Realizado\",\"Reparo / Duração da partida\"]\n]", "GAME_RECORD.SEASON.DATA_INTRO.MASHMARK": "[\n  [\"Eficiência de DPS\",\"Dano ajustado / duração da partida\"],\n  [\"Dano Recebido\",\"Dano recebido / Duração da partida\"],\n  [\"Reparo Realizado\",\"Reparo / Duração da partida\"]\n]", "GAME_RECORD.SEASON.DATA_INTRO.TITLE": "Resu<PERSON> dos dados", "GAME_RECORD.SEASON.DISTANCE_MOVED": "Distância Pilotada", "GAME_RECORD.SEASON.DRIVING_TIME": "Tempo de Jogo", "GAME_RECORD.SEASON.DUNGEON_COMPLETE_COUNT": "Missões Furtivas", "GAME_RECORD.SEASON.DUNGEON_ENTER_COUNT": "Total de Zonas Restritas Acessadas", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_A_COUNT": "Extrações do Ponto A  ", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_B_COUNT": "Extrações do Ponto B  ", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_COUNT": "Extrações", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_C_COUNT": "Extrações do Ponto C  ", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_D_COUNT": "Extrações do Ponto D  ", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_E_COUNT": "Extrações do Ponto E  ", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_F_COUNT": "Extrações do Ponto F", "GAME_RECORD.SEASON.EVACUATE_SUCCESS_RATE": "Taxa de Extrações", "GAME_RECORD.SEASON.GROUP_PARTICIPATION_RATE": "Taxa de Participação de Equipe", "GAME_RECORD.SEASON.KILL": "Eliminações <PERSON>", "GAME_RECORD.SEASON.KILLED": "Total de Mortes", "GAME_RECORD.SEASON.KILL_BOSS": "Eliminações de Alvo Colossal", "GAME_RECORD.SEASON.KILL_CARGO_SHIP": "Naves/Navios Neutralizados", "GAME_RECORD.SEASON.KILL_DEAD_RATIO": "Taxa K/D", "GAME_RECORD.SEASON.KILL_DEFENSE_INSTALLATION": "Instalações de Defesa", "GAME_RECORD.SEASON.KILL_EIC_JAR": "Espécimes de Corite", "GAME_RECORD.SEASON.KILL_EIC_NPC": "Atacantes Contaminados por Corite", "GAME_RECORD.SEASON.KILL_ELITE": "Atacantes de 3ª Geração", "GAME_RECORD.SEASON.KILL_GIANT_INSTALLATION": "Instalações <PERSON>", "GAME_RECORD.SEASON.KILL_PLAYER": "Mercená<PERSON>s", "GAME_RECORD.SEASON.KILL_SCENE_MISC": "Atacantes de 2ª Geração", "GAME_RECORD.SEASON.KILL_STRATUM_SHATTERER": "Extractores de Corite", "GAME_RECORD.SEASON.LAUNCHER_COUNT": "Lançamentos de Foguete de Carga", "GAME_RECORD.SEASON.MULTI_KILL_2": "Eliminação Dupla", "GAME_RECORD.SEASON.MULTI_KILL_3": "Eliminação Tripla", "GAME_RECORD.SEASON.MULTI_KILL_KING": "<PERSON><PERSON>", "GAME_RECORD.SEASON.MVP": "MVP", "GAME_RECORD.SEASON.NOT_ON_RANK": "Não classificado", "GAME_RECORD.SEASON.NO_KILLED_BATTLE_COUNT": "Partidas Perfeitas", "GAME_RECORD.SEASON.RANKING": "Classificação", "GAME_RECORD.SEASON.REPAIR": "Reparo Total", "GAME_RECORD.SEASON.REPAIR_EFFECTIVENESS": "Reparo Realizado", "GAME_RECORD.SEASON.RESCUE": "Total de Aliados Invocados", "GAME_RECORD.SEASON.SEASON": "", "GAME_RECORD.SEASON.SVP": "SVP", "GAME_RECORD.SEASON.TAKEOUT_COUNT": "Suprimentos Extraídos", "GAME_RECORD.SEASON.TASK_POINT": "Total de Pontos de Missão", "GAME_RECORD.SEASON.TOP_ASSIST": "<PERSON><PERSON> Assistência", "GAME_RECORD.SEASON.TOP_CONSECUTIVE_WINS": "<PERSON><PERSON> Vitórias", "GAME_RECORD.SEASON.TOP_DAMAGE": "<PERSON><PERSON>", "GAME_RECORD.SEASON.TOP_DAMAGE_CONTRIBUTION": "<PERSON><PERSON>", "GAME_RECORD.SEASON.TOP_KILL": "<PERSON><PERSON> Eliminações", "GAME_RECORD.SEASON.TOP_RANK": "<PERSON><PERSON><PERSON>", "GAME_RECORD.SEASON.TOP_REPAIR": "<PERSON><PERSON>", "GAME_RECORD.SEASON.TOP_TAKEOUT_COUNT": "<PERSON><PERSON> de Suprimentos Extraída", "GAME_RECORD.SEASON.VICTORY_RATE": "Taxa de Vitória", "GAME_RECORD.SEASON.WINS": "Vitórias", "GAME_RECORD.SEASONDATA.HONOR.OPTIMUM_KILL": "Melhor Finalizador", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.AVG_ASSISTS": "Média de Assistências", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.AVG_DAMAGE_AND_AVG_HEAL": "Média de Participação em Dano/Reparo", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.AVG_DEATHS": "<PERSON><PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.AVG_POINTS": "<PERSON><PERSON>dia de Pontos de Mérito", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.AVG_TASK_POINT": "Pontos de Missão Médios", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.MAX_DAMAGE_DISP_AND_MAX_HEAL": "<PERSON><PERSON> em Dano/Reparo", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.MAX_DEATHS": "<PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.MAX_POINTS": "<PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.TITLE": "<PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.DETAIL_DATA.TOP_TASK_POINT": "<PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.HONOR.COPPER": "<PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.HONOR.DEFAULT_HONOR": "Honra Desconhecida", "GAME_RECORD.SEASON_DATA.HONOR.GIANT_FORT_KILLS": "Torres Grandes destruídas", "GAME_RECORD.SEASON_DATA.HONOR.GOLDEN": "<PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.HONOR.KONSTANSTIN_KILLS": "CONSTANTINE", "GAME_RECORD.SEASON_DATA.HONOR.LARGE_TARGET_KILLS": "Eliminações de chefe", "GAME_RECORD.SEASON_DATA.HONOR.MAKALU_KILLS": "MAKALU", "GAME_RECORD.SEASON_DATA.HONOR.SILVER": "<PERSON><PERSON>", "GAME_RECORD.SEASON_DATA.HONOR.VOTING_TYPE_KILL_HELL_CAT": "HELLCAT", "GAME_RECORD.SEASON_DATA.HONOR.VOTING_TYPE_KILL_MINECORE": "Núcleo de Mina destruído", "GAME_RECORD.SEASON_DATA.MY_HONOR.TITLE": "<PERSON><PERSON>", "GAME_RECORD.TAB.RECORD_DETAIL.DETAIL_TITLE": "<PERSON><PERSON><PERSON>", "GAME_RECORD.TAB.RECORD_DETAIL.TITLE": "Histórico de Jogo", "GAME_RECORD.TAB.SEASON_DATA.TITLE": "<PERSON><PERSON> da Temporada", "GAME_RECORD.TASK_POINT": "Total de Pontos de Missão", "GAME_RECORD.TOTAL_GAMES": "<PERSON><PERSON>", "GAME_RECORD.UNIT.HOUR": " h", "GAME_RECORD.UNIT.MINUTE": " min", "NOMORE": "<PERSON><PERSON> há mais conteúdo disponível~", "OPEN_CHARGE": "Abrindo a recarga", "RANK.LOOKS.DATA_ITEM.CREATOR": "<PERSON><PERSON><PERSON>", "RANK.LOOKS.DATA_ITEM.DOWNLOADS": "Downloads", "RANK.LOOKS.DATA_ITEM.NO": "ID", "RANK.LOOKS.DATA_ITEM.PUBLISH_TIME": "Hora de Publicação", "RANK.LOOKS.IMG_ERROR_TEXT": "Erro ao carregar imagem", "RANK.LOOKS.NO_DATA_TEXT": "Nenhum dado ainda. Acompanhe.", "RANK.LOOKS.REFRESH_TIPS": "Atualiza a cada 30 minutos", "RANK.LOOKS.TAB.FEMALE": "Feminino", "RANK.LOOKS.TAB.MALE": "<PERSON><PERSON><PERSON><PERSON>", "RANK.LOOKS.UNIT.TIMES": "", "TAB.GAME_RECORD": "Resul<PERSON><PERSON>", "TAB.HOMEPAGE": "Página inicial"}}