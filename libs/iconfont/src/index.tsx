import type { JSX } from 'preact';
import type { HTMLAttributes } from 'preact/compat';
import { clsx } from 'clsx';
import { TIcon } from './icon';

export interface IconProps extends HTMLAttributes<HTMLElement> {
  icon: TIcon;
  className?: string | JSX.SignalLike<string>;
}

export function Icon(props: IconProps) {
  const { icon, className, ...rest } = props;
  return <i className={clsx('icomoon', `icon-${icon}`, className)} {...rest} />;
}

export type { TIcon } from './icon';
