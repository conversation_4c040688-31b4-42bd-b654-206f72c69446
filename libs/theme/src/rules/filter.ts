import { Rule } from 'unocss';
import { Theme } from '../theme';

export const filterBase = {
  '--ssg-blur': ' ',
  '--ssg-brightness': ' ',
  '--ssg-contrast': ' ',
  '--ssg-drop-shadow': ' ',
  '--ssg-grayscale': ' ',
  '--ssg-hue-rotate': ' ',
  '--ssg-invert': ' ',
  '--ssg-saturate': ' ',
  '--ssg-sepia': ' ',
};
const filterProperty =
  'var(--ssg-blur) var(--ssg-brightness) var(--ssg-contrast) var(--ssg-drop-shadow) var(--ssg-grayscale) var(--ssg-hue-rotate) var(--ssg-invert) var(--ssg-saturate) var(--ssg-sepia)';
export const backdropFilterBase = {
  '--ssg-backdrop-blur': ' ',
  '--ssg-backdrop-brightness': ' ',
  '--ssg-backdrop-contrast': ' ',
  '--ssg-backdrop-grayscale': ' ',
  '--ssg-backdrop-hue-rotate': ' ',
  '--ssg-backdrop-invert': ' ',
  '--ssg-backdrop-opacity': ' ',
  '--ssg-backdrop-saturate': ' ',
  '--ssg-backdrop-sepia': ' ',
};
const backdropFilterProperty =
  'var(--ssg-backdrop-blur) var(--ssg-backdrop-brightness) var(--ssg-backdrop-contrast) var(--ssg-backdrop-grayscale) var(--ssg-backdrop-hue-rotate) var(--ssg-backdrop-invert) var(--ssg-backdrop-opacity) var(--ssg-backdrop-saturate) var(--ssg-backdrop-sepia)';

export const filters: Rule<Theme>[] = [
  [
    /^(?:(backdrop-)|filter-)?blur(?:-(.+))?$/,
    ([, b, s], { theme }) => {
      const value = theme.blur?.[s];

      if (value !== '') {
        if (b) {
          return {
            [`--ssg-${b}blur`]: `blur(${value})`,
            '-webkit-backdrop-filter': backdropFilterProperty,
            'backdrop-filter': backdropFilterProperty,
          };
        } else {
          return {
            [`--ssg-blur`]: `blur(${value})`,
            filter: filterProperty,
          };
        }
      }
      return {};
    },
    { autocomplete: ['(backdrop|filter)-blur-$blur', 'blur-$blur', 'filter-blur'] },
  ],
  [
    /^(?:(backdrop-)|filter-)?brightness(?:-(.+))?$/,
    ([, b, s]) => {
      const value = s;

      if (value !== '') {
        const val = Number(s) / 100;
        if (b) {
          return {
            [`--ssg-${b}brightness`]: `brightness(${val})`,
            '-webkit-backdrop-filter': backdropFilterProperty,
            'backdrop-filter': backdropFilterProperty,
          };
        } else {
          return {
            [`--ssg-brightness`]: `brightness(${val})`,
            filter: filterProperty,
          };
        }
      }
      return {};
    },
    { autocomplete: ['(backdrop|filter)-brightness', 'brightness', 'filter-brightness'] },
  ],
];
