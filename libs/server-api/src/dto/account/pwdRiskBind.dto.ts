import { type ZodType } from 'zod';
import * as z from 'zod';
import { PwdRiskLoginDTO, pwdRiskLoginSchema } from './pwdRiskLogin.dto';

/**
 * 密码登录 DTO（风控模式） - Schema
 */
export const pwdRiskBindSchema = pwdRiskLoginSchema;

export type IPwdRiskBind = z.infer<typeof pwdRiskBindSchema>;

/**
 * 密码登录 DTO（风控模式）
 */
export class PwdRiskBindDTO extends PwdRiskLoginDTO {
  static of(data: IPwdRiskBind): PwdRiskBindDTO {
    return new PwdRiskBindDTO(data);
  }

  get schema(): ZodType<IPwdRiskBind> {
    return pwdRiskBindSchema;
  }
}

// unit test
if (import.meta.vitest) {
  const { expect, test } = import.meta.vitest;

  test('of', () => {
    const data: IPwdRiskBind = {
      account: '1234',
      password: '123',
      serialNumber: '123',
    };

    const dto = PwdRiskBindDTO.of(data);

    expect(dto).toBeInstanceOf(PwdRiskBindDTO);

    expect(dto.account).toBe(data.account);
    expect(dto.password).toBe(data.password);
    expect(dto.serialNumber).toBe(data.serialNumber);
  });

  test('验证数据（正常情况）', async () => {
    const data: IPwdRiskBind = {
      account: '1234',
      password: '123',
      serialNumber: '123',
    };

    const validDto = PwdRiskBindDTO.of(data);
    await expect(validDto.validate()).resolves.not.toThrow();
    await expect(validDto.validate()).resolves.toStrictEqual(data);
  });

  test('缺斤少两', async () => {
    const data: IPwdRiskBind = {
      account: '1234',
      // password: '123',
      serialNumber: '123',
    } as unknown as IPwdRiskBind;

    const validDto = PwdRiskBindDTO.of(data);
    await expect(validDto.validate()).rejects.toThrow(z.ZodError);
  });

  test('类型不符合要求', async () => {
    const data: IPwdRiskBind = {
      account: '123',
      password: 123,
      serialNumber: '123',
    } as unknown as IPwdRiskBind;

    const validDto = PwdRiskBindDTO.of(data);
    await expect(validDto.validate()).rejects.toThrow(z.ZodError);
  });

  test('内容不符合要求', async () => {
    const data: IPwdRiskBind = {
      account: '123',
      password: '123',
      serialNumber: new Date(),
    } as unknown as IPwdRiskBind;

    const validDto = PwdRiskBindDTO.of(data);
    await expect(validDto.validate()).rejects.toThrow(z.ZodError);
  });
}
