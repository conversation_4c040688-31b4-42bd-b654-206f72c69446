import type { AsukaRequest } from '@tuilan/asuka-request';
import { RiskCodeDTO, ValidateRiskCodeDTO, ValidateVerifyCodeDTO, VerifyCodeDTO } from '../dto';
import { AccountSmsVerifyCodeRemote } from '../remote/accountSmsVerifyCode.remote';

export class SMSApi {
  constructor(private readonly request: AsukaRequest) {}

  async getSMSVerifyCode(dto: VerifyCodeDTO) {
    await dto.validate();

    return this.request.post<AccountSmsVerifyCodeRemote>('/verify-code/send-verify-code', dto);
  }

  async validateSMSVerifyCode(dto: ValidateVerifyCodeDTO) {
    await dto.validate();

    return this.request.post('/verify-code/validate-verify-code', dto);
  }

  async getRiskVerifyCode(dto: RiskCodeDTO) {
    await dto.validate();
    return this.request.post<AccountSmsVerifyCodeRemote>('/risk/send-verify-code', dto);
  }

  async validateRiskVerifyCode(dto: ValidateRiskCodeDTO) {
    await dto.validate();
    return this.request.post('/risk/validate-verify-code', dto);
  }
}
