import type { HTMLAttributes } from 'preact/compat';
import type { IconProps } from '@ssglauncher/iconfont/src';
import type { Ref } from 'preact';
import { useEffect } from 'preact/compat';
import { Icon } from '@ssglauncher/iconfont';
import { useCallback, useRef } from 'preact/hooks';
import { clsx } from 'clsx';

export interface PopoverMenuProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * 触发关闭
   */
  onClose: () => void;
  /**
   * 配置项, null 代表分割线
   */
  items: (PopoverMenuItemProps | null)[];
}

export interface PopoverMenuItemProps {
  icon: IconProps['icon'];
  label: string;
  action?: string;
  onClick: () => void;
  disable?: boolean;
}

function PopoverMenuItem({ icon, label, onClick, disable, action }: PopoverMenuItemProps) {
  const renderIcon = useCallback(() => {
    if (/^https?:/.test(icon || '')) {
      return <img src={icon} alt="icon" className="w-16px h-16px pointer-events-none" />;
    } else {
      return <Icon data-icon={icon} icon={icon || 'repair'} className="text-size-16px color-inherit" />;
    }
  }, [icon]);

  return (
    <a
      data-action={action}
      className={clsx(
        'block w-auto h-32px flex items-center p-8px rounded-2px transition-colors cursor-pointer',
        disable && 'op-40 cursor-not-allowed',
        !disable && 'hover:bg-white-8',
        'color-white-60 hover:color-white-100',
      )}
      onClick={disable ? undefined : onClick}
    >
      <span className="w-16px mr-8px -mt-px h-5 flex items-center">{renderIcon()}</span>
      <span className="w-auto text-size-12px leading-5 color-inherit text-left whitespace-nowrap flex-grow-1">
        {label}
      </span>
    </a>
  );
}

function PopoverMenuDivider() {
  return <div className="h-1px my-4px bg-white-4" />;
}

function PopoverMenu({ className, onClose, items, ...rest }: PopoverMenuProps) {
  const rootRef = useRef<HTMLDivElement>();

  useEffect(() => {
    const onClick = (event: Event) => {
      const targetElement = event.target as Node;

      if (!rootRef) {
        return;
      }

      if (!rootRef.current?.contains(targetElement) && targetElement !== rootRef.current) {
        onClose();
      }
    };
    document.addEventListener('click', onClick);
    return () => {
      document.removeEventListener('click', onClick);
    };
  }, []);

  const renderContent = useCallback(() => {
    return items.map((item, key) => {
      if (item === null) {
        return <PopoverMenuDivider key={key} />;
      } else {
        return <PopoverMenuItem key={item.label} {...item} />;
      }
    });
  }, [items]);

  return (
    <div
      className={clsx('z-99 bg-black-94 w-auto rounded-small p-4px min-w-140px', className)}
      ref={rootRef as Ref<HTMLDivElement>}
      {...rest}
    >
      {renderContent()}
    </div>
  );
}

export { PopoverMenu };
