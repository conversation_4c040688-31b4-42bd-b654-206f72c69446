import React, { PropsWithChildren } from 'preact/compat';
import { Icon } from '@ssglauncher/iconfont';
import { useMemo } from 'react';
import { clsx } from 'clsx';

interface RadioProps {
  size: number;
  className?: string;
  checked: boolean;

  onChange?: (checked: boolean) => void;
}

export function Radio({ checked, size, className, onChange, children }: PropsWithChildren<RadioProps>) {
  const content = useMemo(() => {
    if (checked) {
      return (
        <Icon
          icon="selected"
          className="text-white-60 hover:text-white-100 block transition-colors"
          style={{ fontSize: size }}
        />
      );
    }
    return (
      <svg
        style={{ transform: `scale(${size / 48})`, transformOrigin: `top left` }}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="48"
        height="48"
        viewBox="0 0 48 48"
      >
        <g>
          <g>
            <rect x="0" y="0" width="48" height="48" rx="0" fill="#D8D8D8" fillOpacity="0" />
          </g>
          <g>
            <g>
              <path
                d="M45,24Q45,24.5155,44.9747,25.0304Q44.9494,25.5453,44.8989,26.0584Q44.8483,26.5714,44.7727,27.0813Q44.6971,27.5913,44.5965,28.0969Q44.4959,28.6025,44.3706,29.1026Q44.2454,29.6027,44.0957,30.096Q43.9461,30.5893,43.7724,31.0747Q43.5987,31.5601,43.4015,32.0363Q43.2042,32.5126,42.9838,32.9787Q42.7634,33.4447,42.5203,33.8993Q42.2773,34.354,42.0123,34.7962Q41.7473,35.2383,41.4609,35.667Q41.1744,36.0956,40.8674,36.5097Q40.5603,36.9238,40.2332,37.3223Q39.9062,37.7208,39.56,38.1027Q39.2138,38.4847,38.8492,38.8492Q38.4847,39.2138,38.1027,39.56Q37.7208,39.9062,37.3223,40.2332Q36.9238,40.5603,36.5097,40.8673Q36.0956,41.1744,35.667,41.4609Q35.2383,41.7473,34.7962,42.0123Q34.354,42.2773,33.8993,42.5203Q33.4447,42.7634,32.9787,42.9838Q32.5126,43.2042,32.0363,43.4015Q31.5601,43.5987,31.0747,43.7724Q30.5893,43.9461,30.096,44.0957Q29.6027,44.2454,29.1026,44.3707Q28.6025,44.4959,28.0969,44.5965Q27.5913,44.6971,27.0813,44.7727Q26.5714,44.8484,26.0584,44.8989Q25.5453,44.9494,25.0304,44.9747Q24.5155,45,24,45Q23.4845,45,22.9696,44.9747Q22.4547,44.9494,21.9416,44.8989Q21.4286,44.8484,20.9187,44.7727Q20.4087,44.6971,19.9031,44.5965Q19.3975,44.4959,18.897399999999998,44.3707Q18.3973,44.2454,17.904,44.0957Q17.4107,43.9461,16.9253,43.7724Q16.4399,43.5987,15.9636,43.4015Q15.4874,43.2042,15.0213,42.9838Q14.5553,42.7634,14.1007,42.5203Q13.646,42.2773,13.2038,42.0123Q12.76166,41.7473,12.33302,41.4609Q11.90438,41.1744,11.49031,40.8674Q11.07624,40.5603,10.67774,40.2332Q10.27924,39.9062,9.89726,39.56Q9.51529,39.2138,9.15076,38.8492Q8.78623,38.4847,8.44003,38.1027Q8.093820000000001,37.7208,7.76678,37.3223Q7.43974,36.9238,7.13264,36.5097Q6.82555,36.0956,6.53914,35.667Q6.25273,35.2383,5.9877,34.7962Q5.72267,34.354,5.4796499999999995,33.8993Q5.2366399999999995,33.4447,5.016220000000001,32.9787Q4.7958099999999995,32.5126,4.59853,32.0363Q4.40125,31.5601,4.22757,31.0747Q4.0539000000000005,30.5893,3.9042529999999998,30.096Q3.7546049999999997,29.6027,3.629344,29.1026Q3.504082,28.6025,3.403509,28.0969Q3.302936,27.5913,3.227293,27.0813Q3.151651,26.5714,3.101121,26.0584Q3.0505908,25.5453,3.0252954,25.0304Q3,24.5155,3,24Q3,23.4845,3.0252954,22.9696Q3.0505908,22.4547,3.101121,21.9416Q3.151651,21.4286,3.227293,20.9187Q3.302936,20.4087,3.403509,19.9031Q3.504082,19.3975,3.629344,18.897399999999998Q3.7546049999999997,18.3973,3.9042529999999998,17.904Q4.0539000000000005,17.4107,4.22757,16.9253Q4.40125,16.4399,4.59853,15.9636Q4.7958099999999995,15.4874,5.016220000000001,15.0213Q5.2366399999999995,14.5553,5.4796499999999995,14.1007Q5.72267,13.646,5.9877,13.2038Q6.25273,12.76166,6.53914,12.33302Q6.82555,11.90438,7.13264,11.49031Q7.43974,11.07624,7.76678,10.67774Q8.093820000000001,10.27924,8.44003,9.89726Q8.78623,9.51529,9.15076,9.15076Q9.51529,8.78623,9.89726,8.44003Q10.27924,8.093820000000001,10.67774,7.76678Q11.07624,7.43974,11.49031,7.13264Q11.90438,6.82555,12.33302,6.53914Q12.76166,6.25273,13.2038,5.9877Q13.646,5.72267,14.1007,5.4796499999999995Q14.5553,5.2366399999999995,15.0213,5.016220000000001Q15.4874,4.7958099999999995,15.9636,4.59853Q16.4399,4.40125,16.9253,4.22757Q17.4107,4.0539000000000005,17.904,3.9042529999999998Q18.3973,3.7546049999999997,18.897399999999998,3.629344Q19.3975,3.504082,19.9031,3.403509Q20.4087,3.302936,20.9187,3.227293Q21.4286,3.151651,21.9416,3.101121Q22.4547,3.0505908,22.9696,3.0252954Q23.4845,3,24,3Q24.5155,3,25.0304,3.0252954Q25.5453,3.0505908,26.0584,3.101121Q26.5714,3.151651,27.0813,3.227293Q27.5913,3.302936,28.0969,3.403509Q28.6025,3.504082,29.1026,3.629344Q29.6027,3.7546049999999997,30.096,3.9042529999999998Q30.5893,4.0539000000000005,31.0747,4.22757Q31.5601,4.40125,32.0363,4.59853Q32.5126,4.7958099999999995,32.9787,5.016220000000001Q33.4447,5.2366399999999995,33.8993,5.4796499999999995Q34.354,5.72267,34.7962,5.9877Q35.2383,6.25273,35.667,6.53914Q36.0956,6.82555,36.5097,7.13264Q36.9238,7.43974,37.3223,7.76678Q37.7208,8.093820000000001,38.1027,8.44003Q38.4847,8.78623,38.8492,9.15076Q39.2138,9.51529,39.56,9.89726Q39.9062,10.27924,40.2332,10.67774Q40.5603,11.07624,40.8673,11.49031Q41.1744,11.90438,41.4609,12.33302Q41.7473,12.76166,42.0123,13.2038Q42.2773,13.646,42.5203,14.1007Q42.7634,14.5553,42.9838,15.0213Q43.2042,15.4874,43.4015,15.9636Q43.5987,16.4399,43.7724,16.9253Q43.9461,17.4107,44.0957,17.904Q44.2454,18.3973,44.3707,18.897399999999998Q44.4959,19.3975,44.5965,19.9031Q44.6971,20.4087,44.7727,20.9187Q44.8483,21.4286,44.8989,21.9416Q44.9494,22.4547,44.9747,22.9696Q45,23.4845,45,24Z"
                fill="#FFFFFF"
                fillOpacity="0.07999999821186066"
              />
            </g>
            <g>
              <path
                d="M24,45C35.598,45,45,35.598,45,24C45,12.40202,35.598,3,24,3C12.40202,3,3,12.40202,3,24C3,35.598,12.40202,45,24,45ZM39.8064,17.7424Q41,20.7574,41,24Q41,27.2426,39.8064,30.2576Q38.5104,33.5312,36.0208,36.0208Q33.5312,38.5104,30.2576,39.8064Q27.2426,41,24,41Q20.7574,41,17.7424,39.8064Q14.4688,38.5104,11.97918,36.0208Q9.48959,33.5312,8.1936,30.2576Q7,27.2426,7,24Q7,20.7574,8.1936,17.7424Q9.48959,14.4688,11.97918,11.97918Q14.4688,9.48959,17.7424,8.1936Q20.7574,7,24,7Q27.2426,7,30.2576,8.1936Q33.5312,9.48959,36.0208,11.97919Q38.5104,14.4688,39.8064,17.7424Z"
                fillRule="evenodd"
                fill="#FFFFFF"
                fillOpacity="0.07999999821186066"
              />
            </g>
          </g>
        </g>
      </svg>
    );
  }, [checked, size]);

  return (
    <label
      className={clsx(className, 'flex', 'items-center cursor-pointer opacity-80 hover:op100')}
      onClick={() => onChange?.(true)}
    >
      <div style={{ width: size, height: size }}>{content}</div>
      {children && <span className={clsx('text-body pl-4px color-white')}>{children}</span>}
    </label>
  );
}
