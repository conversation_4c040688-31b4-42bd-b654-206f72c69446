import type { AsukaRequest } from '@tuilan/asuka-request';
import { getLang, getLogger } from '@ssglauncher/utils';
import { createCustomerServiceUrlRepo } from '../repos/customerService.repo';
import { getGameInfo } from '../../gameManagement';
import { getGameChannelConfig } from '../../gameChannel';

export const createCustomerService =
  (request: AsukaRequest) =>
  async (appCode: string): Promise<string> => {
    const gameInfo = await getGameInfo(appCode);
    if (!gameInfo.customerServiceSkipLogin) {
      const channelConfig = await getGameChannelConfig();
      return replaceLang(channelConfig.ConnectServiceUrl);
    }
    let authUrl;
    try {
      const getCustomerServiceUrl = createCustomerServiceUrlRepo(request);
      const res = await getCustomerServiceUrl(appCode);
      authUrl = res?.authUrl || res?.url;
    } catch (e) {
      const logger = getLogger('biz-core');
      logger('/user/get-gm-auth-url err', e);
      const channelConfig = await getGameChannelConfig();
      authUrl = replaceLang(channelConfig.ConnectServiceUrl);
    }
    return authUrl;
  };

function replaceLang(url: string) {
  /** 客服链接的占位符为{lang}，遇到则替换成当前选中语言 */
  const replacement = '{lang}';
  return url?.replace(replacement, getLang());
}
