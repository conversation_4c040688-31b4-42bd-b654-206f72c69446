/** 子应用激活事件 */
export const EVENT_SUB_APP_ACTIVATED = 'activated';

/** 子应用停用事件 */
export const EVENT_SUB_APP_DEACTIVATED = 'deactivated';

export interface MessageEventData<T = any> {
  type: string;
  detail?: T;
}

/**
 * 向指定iframe发送消息
 */
export function postMessage(name: string, data: MessageEventData) {
  const iframe = window.document.querySelector<HTMLIFrameElement>(`iframe[name="${name}"]`);
  iframe?.contentWindow?.postMessage(data);
}
