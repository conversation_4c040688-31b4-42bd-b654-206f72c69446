import type { HTMLAttributes } from 'preact/compat';
import { clsx } from 'clsx';

interface SecondaryButtonProps extends HTMLAttributes<HTMLButtonElement> {
  className?: string;
}

export function SecondaryButton({ children, disabled, className, ...rest }: SecondaryButtonProps) {
  return (
    <button
      className={clsx(
        'w-full h-42px select-none py-[10px] bg-white-8 hover:bg-white-20 text-white-80 flex items-center justify-center rounded-medium outline-none text-title tracking-widest',
        disabled && 'opacity-40 cursor-not-allowed',
        className,
      )}
      type="submit"
      disabled={disabled}
      {...rest}
    >
      {children}
    </button>
  );
}
