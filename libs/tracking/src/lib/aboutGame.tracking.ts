import { BaseTracking } from './base.tracking';

export class AboutGameTracking extends BaseTracking {
  /**
   * 1、菜单栏高亮后可点击状态时，用户触发上报
   * 2、已有游戏进程的前提下，用户点击也需上报
   * （游戏后台免登开关打开或关闭，点击都要上报）
   */
  ssgStartGame(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项
     */
    // navListCat: string;
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项
     */
    // gameVersion: string;
    /**
     * 当前是否有游戏进程：1-有，0-无
     */
    // isingame: '1' | '0';
    /**
     * 用户所选的区服
     */
    servername: string;
    /**
     * 用户所选的区服ping值
     */
    serPingValue: string;
    /**
     * 角色在线离线状态：1-在线，0-离线, -1-未知
     */
    characterStatus: number;
  }) {
    this.report({
      key: 'ssgStartGame',
      desc: '开始游戏',
      ext,
    });
  }
  /**
   * 已有游戏进程的前提下，用户点击【进入游戏】，打开游戏客户端
   */
  ssgMultOnGame(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项
     */
    navListCat: string;
    /**
     * 启动游戏的版本号
     */
    gameVersion: string;
  }) {
    this.report({
      key: 'ssgMultOnGame',
      desc: '多开-打开游戏',
      ext,
    });
  }

  /**
   * 已有游戏进程的前提下，用户点击【选择账号】，走账号绑定流程
   */
  ssgMultChooseAccount() {
    this.report({
      key: 'ssgMultChooseAccount',
      desc: '多开-选择账号',
      ext: {},
    });
  }

  /**
   * 已有游戏进程的前提下，用户点击【确认】，走账号绑定流程
   */
  ssgCertifyMultChooseAccount() {
    this.report({
      key: 'ssgCertifyMultChooseAccount',
      desc: '多开-确认选择账号',
      ext: {},
    });
  }

  // ------------------------------------------- 华丽风格先 使用启动器-区服选择 ----------------------------------
  /**
   * 区服选择
   */
  ssgChooseZone(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项
     */
    navListCat: string;
  }) {
    this.report({
      key: 'ssgChooseZone',
      desc: '区服选择',
      ext,
    });
  }

  /**
   * 触发区服选择弹窗后，用户点击【取消】
   */
  ssgAckCancelChooseZone(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项
     */
    navListCat: string;
  }) {
    this.report({
      key: 'ssgAckCancelChooseZone',
      desc: '弹窗取消区服选择',
      ext,
    });
  }

  /**
   * 触发区服选择弹窗后，用户点击【确认】
   */
  ssgAckChooseZone(ext: {
    /**
     * 区服
     */
    server: string;
    /**
     * 区服id
     */
    serverId: string;
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项
     */
    navListCat: string;
  }) {
    this.report({
      key: 'ssgAckChooseZone',
      desc: '弹窗确认区服选择',
      ext,
    });
  }

  /**
   * 用户点击区服入口时上报 (新区服 for mb)
   */
  ssgClickServerFirst() {
    this.report({
      key: 'ssgClickServerFirst',
      desc: '点击区服入口',
      ext: {},
    });
  }

  /**
   * 用户打开区服列表点击具体区服 (新区服 for mb)
   */
  ssgSeverSelect(ext: {
    /** 本次选择区服：(例：亚太服） */
    presentSeverChoose: string;
  }) {
    this.report({
      key: 'ssgSeverSelect',
      desc: '选择区服',
      ext,
    });
  }

  // ------------------------------------------- 华丽风格先 使用启动器-卸载游戏 ----------------------------------
  /**
   * 用户触发弹窗按钮进行卸载
   */
  ssgAckUninstallGame(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项
     */
    navListCat: string;
    /**
     * 卸载游戏的版本号
     */
    gameVersion: string;
  }) {
    this.report({
      key: 'ssgAckUninstallGame',
      desc: '弹窗确认卸载游戏',
      ext,
    });
  }

  /**
   * 用户触发弹窗按钮取消卸载
   */
  ssgAckCancelUninstallGame(ext: {
    /**
     * <2>
     */
    navListCat: string;
    gameVersion: string;
  }) {
    this.report({
      key: 'ssgAckCancelUninstallGame',
      desc: '弹窗确认取消卸载游戏',
      ext,
    });
  }

  ssgGameCharacterStatusSwitch(ext: {
    /**
     * 角色在线离线状态：1-在线，0-离线, -1-未知
     */
    characterStatus: number;
  }) {
    this.report({
      key: 'SSGGameCharacterStatusSwitch',
      desc: '用户切换启动器角色在线离线状态进入游戏',
      ext,
    });
  }
}
