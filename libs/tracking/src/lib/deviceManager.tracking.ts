import { BaseTracking } from './base.tracking';

export class DeviceManagerTracking extends BaseTracking {
  /**
   * 移除设备
   */
  ssgRemoveDevice(ext: {
    /**
     * 金山通行证，账号信息
     */
    preAccountId?: string;
    /**
     * 移除的设备信息
     */
    afterDeviceId?: string;
  }) {
    this.report({
      key: 'ssgRemoveDevice',
      desc: '移除设备',
      ext,
    });
  }

  /**
   * 确认移除设备
   */
  ssgAckRemoveDevice() {
    this.report({
      key: 'ssgAckRemoveDevice',
      desc: '确认移除设备',
      ext: {},
    });
  }

  /**
   * 取消移除设备
   */
  ssgAckCancelRemoveDevice() {
    this.report({
      key: 'ssgAckCancelRemoveDevice',
      desc: '取消移除设备',
      ext: {},
    });
  }

  /**
   * 移除设备成功
   */
  ssgRemoveDeviceSuccess(ext: {
    /**
     * 金山通行证，账号信息
     */
    preAccountId?: string;
    /**
     * 移除的设备信息
     */
    afterDeviceId?: string;
  }) {
    this.report({
      key: 'ssgRemoveDeviceSuccess',
      desc: '移除设备成功',
      ext,
    });
  }

  /**
   * 移除设备失败
   */
  ssgRemoveDeviceFail(ext: {
    /**
     * 金山通行证，账号信息
     */
    preAccountId?: string;
    /**
     * 移除的设备信息
     */
    afterDeviceId?: string;
    /**
     * 失败原因，枚举值
     */
    code: number;
    /**
     * 失败原因
     */
    statusReason: string;
  }) {
    this.report({
      key: 'ssgRemoveDeviceFail',
      desc: '移除设备失败',
      ext,
    });
  }
}
