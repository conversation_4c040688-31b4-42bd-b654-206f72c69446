import { compareIndex, logger } from '../utils';

export type NavKey = 'up' | 'down' | 'left' | 'right' | 'enter' | 'esc';
export type NavMode = 'x' | 'y' | 'grid' | 'none';
export type NavIndex = `${number}-${string}`;
export type NavGroup = `${number}` | NavIndex;

type MultiNavGroup = NavGroup | `${NavGroup},${NavGroup}` | `${NavGroup},${NavGroup},${NavGroup}`;
type NavKeyBinding = { [key in NavKey]?: 'none' | 'click' | MultiNavGroup };

/** 导航元素数据集 */
export type NavDataset = {
  /**
   * 分组索引
   * @example
   *  - “0”: 分组0
   *  - “0-1”: index=1的子分组1
   */
  group: NavGroup;
  /**
   * 元素完整索引
   * @example
   * - "0-0"
   * - "0-1-0"
   */
  index: NavIndex;
  /**
   * 导航模式
   */
  nav: NavMode;
  /**
   * 进入嵌套导航的Key
   */
  subNav: NavKey;
  /**
   * 网格布局列数
   */
  gridCol: `${number}`;
  /**
   * 是否为默认激活元素
   */
  asDefault?: 'true';
} & NavKeyBinding;

export type NavAttrs = {
  [key: `data-${string}`]: string;
} & {
  tabIndex: -1;
  autoFocus?: boolean;
};

/**
 * 设置导航元素属性
 *
 * @param index 分组索引
 * @param nav 导航模式
 * @param attrs 其他属性
 */
export function navAttrs(
  index: NavIndex,
  nav: NavMode,
  attrs?: Partial<Pick<NavDataset, 'subNav'>> &
    NavKeyBinding & {
      gridCol?: number;
      asDefault?: boolean;
      autoFocus?: boolean;
    },
): NavAttrs | undefined {
  if (!index) return;
  const i = index.lastIndexOf('-');
  if (i === -1) {
    logger.tag('navAttrs()').warn('index must be like "0-0" or "0-1-0"');
    return;
  }
  const group = index.substring(0, i);
  const { autoFocus, ...restAttrs } = attrs || {};
  const dataAttrs = Object.entries(restAttrs).reduce((res, [key, value]) => {
    if (value !== undefined && value !== false) {
      const attrKey = key.replace(/[A-Z]/g, match => `-${match.toLowerCase()}`);
      res[`data-${attrKey}`] = value.toString();
    }
    return res;
  }, {} as Record<string, string>);

  return {
    'data-nav': nav,
    'data-group': group,
    'data-index': index,
    ...dataAttrs,
    autoFocus,
    tabIndex: -1,
  };
}

navAttrs.index = (...indexes: (number | string)[]) => {
  return indexes.join('-').replace(/-{2,}/g, '-') as NavIndex;
};

navAttrs.indexes = (...indexes: (number | string)[][]) => {
  return indexes.map(indexes => navAttrs.index(...indexes)).join(',') as MultiNavGroup;
};

/** 导航元素 */
export interface NavElement extends HTMLElement {
  dataset: NavDataset & DOMStringMap;
}

function isHTMLElement(el: unknown): el is HTMLElement {
  return !!el && typeof el === 'object' && 'nodeType' in el && el.nodeType === 1;
}

export function isNavElement(el: unknown): el is NavElement {
  return isHTMLElement(el) && !!el.dataset.nav && !!el.dataset.group && !!el.dataset.index;
}

export function isNavigable(el: NavElement) {
  return el.dataset.nav !== 'none';
}

export function isVisible(el: HTMLElement) {
  if (!el?.isConnected) return false;

  const style = window.getComputedStyle(el);
  if (style.display === 'none' || style.visibility === 'hidden' || parseFloat(style.opacity) < 0.1) {
    return false;
  }

  return true;
}

type RootDocument = Document | ShadowRoot;

interface WujieAppElement extends HTMLElement {
  tagName: 'WUJIE-APP';
  shadowRoot: ShadowRoot;
}

function isWujieAppElement(el: unknown): el is WujieAppElement {
  return isHTMLElement(el) && el.tagName === 'WUJIE-APP' && el.shadowRoot?.mode === 'open';
}

function queryWujieAppElementAll(root: RootDocument) {
  return Array.from(root.querySelectorAll<WujieAppElement>('wujie-app')).filter(
    el => isWujieAppElement(el) && isVisible(el),
  );
}

/** 选择器匹配所有导航元素（包含wujie文档）并按index排序 */
export function querySelectorAll(selectors: string, root: RootDocument = document) {
  const elements = Array.from(root.querySelectorAll<NavElement>(selectors))
    .filter(el => isVisible(el) && isNavElement(el))
    .sort((a, b) => compareIndex(a.dataset.index, b.dataset.index));
  elements.push(...queryWujieAppElementAll(root).flatMap(it => querySelectorAll(selectors, it.shadowRoot)));
  return elements;
}

/** 选择器匹配第一个导航元素（包含wujie文档） */
export function querySelector(selectors: string, root: RootDocument = document): NavElement | null {
  const el = root.querySelector<NavElement>(selectors);
  if (isNavElement(el)) return el;

  for (const { shadowRoot } of queryWujieAppElementAll(root)) {
    const el = querySelector(selectors, shadowRoot);
    if (el) return el;
  }

  return null;
}

/** 获取拥有焦点的元素（包含wujie文档） */
export function getActiveElement(root: RootDocument = document) {
  const activeElement = root.activeElement as HTMLElement | null;

  if (isWujieAppElement(activeElement)) {
    return getActiveElement(activeElement.shadowRoot);
  }

  return activeElement;
}

/** 获取默认导航元素（包含wujie文档） */
export function getDefaultNavElement() {
  const elements = querySelectorAll('[data-nav]').filter(isNavigable);
  for (const el of elements) {
    if (el.hasAttribute('autofocus') || el.dataset.asDefault) return el;
  }

  return elements[0] || null;
}
